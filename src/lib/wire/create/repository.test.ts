import { describe, it, expect, vi, beforeEach } from "vitest";

import { createWireRecord } from "./repository";

import type { CreateWireRecordProperties, Wire } from "./types";
import type { PrismaClient } from "@prisma/client";

const mockPrisma = {
  wire: {
    create: vi.fn(),
  },
};

describe("createWireRecord", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const mockWireProperties: CreateWireRecordProperties = {
    expectedWireDate: new Date("2024-01-15"),
    finalWireDate: new Date("2024-01-16"),
    expectedWireAmount: 1000.5,
    finalWireAmount: 1000.5,
    customerId: 1,
    settlementId: 2,
    frequencyId: 3,
    isHold: false,
    reference: "TEST-REF-001",
    paymentRailId: 4,
  };

  const mockWireResponse: Wire = {
    wireId: 1,
    expectedWireDate: new Date("2024-01-15"),
    finalWireDate: new Date("2024-01-16"),
    expectedWireAmount: 1000.5,
    finalWireAmount: 1000.5,
    isHold: false,
    reference: "TEST-REF-001",
    frequencyId: 3,
    isCancelled: false,
    customerId: 1,
    settlementId: 2,
    paymentRailId: 4,
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T10:00:00Z",
  };

  it("should create a wire record with all properties", async () => {
    mockPrisma.wire.create.mockResolvedValue(mockWireResponse);

    const result = await createWireRecord(
      mockPrisma as unknown as PrismaClient,
      mockWireProperties
    );

    expect(mockPrisma.wire.create).toHaveBeenCalledWith({
      data: {
        expectedWireDate: mockWireProperties.expectedWireDate,
        finalWireDate: mockWireProperties.finalWireDate,
        expectedWireAmount: mockWireProperties.expectedWireAmount,
        finalWireAmount: mockWireProperties.finalWireAmount,
        customerId: mockWireProperties.customerId,
        settlementId: mockWireProperties.settlementId,
        frequencyId: mockWireProperties.frequencyId,
        isHold: mockWireProperties.isHold,
        reference: mockWireProperties.reference,
        paymentRailId: mockWireProperties.paymentRailId,
      },
    });
    expect(result).toEqual(mockWireResponse);
  });

  it("should create a wire record with null reference when reference is not provided", async () => {
    const propertiesWithoutReference: CreateWireRecordProperties = {
      expectedWireDate: mockWireProperties.expectedWireDate,
      finalWireDate: mockWireProperties.finalWireDate,
      expectedWireAmount: mockWireProperties.expectedWireAmount,
      finalWireAmount: mockWireProperties.finalWireAmount,
      customerId: mockWireProperties.customerId,
      settlementId: mockWireProperties.settlementId,
      frequencyId: mockWireProperties.frequencyId,
      isHold: mockWireProperties.isHold,
      paymentRailId: mockWireProperties.paymentRailId,
    };
    mockPrisma.wire.create.mockResolvedValue(mockWireResponse);

    await createWireRecord(
      mockPrisma as unknown as PrismaClient,
      propertiesWithoutReference
    );

    expect(mockPrisma.wire.create).toHaveBeenCalledWith({
      data: {
        expectedWireDate: propertiesWithoutReference.expectedWireDate,
        finalWireDate: propertiesWithoutReference.finalWireDate,
        expectedWireAmount: propertiesWithoutReference.expectedWireAmount,
        finalWireAmount: propertiesWithoutReference.finalWireAmount,
        customerId: propertiesWithoutReference.customerId,
        settlementId: propertiesWithoutReference.settlementId,
        frequencyId: propertiesWithoutReference.frequencyId,
        isHold: propertiesWithoutReference.isHold,
        reference: null,
        paymentRailId: propertiesWithoutReference.paymentRailId,
      },
    });
  });

  it("should create a wire record with null paymentRailId when paymentRailId is not provided", async () => {
    const propertiesWithoutPaymentRailId: CreateWireRecordProperties = {
      expectedWireDate: mockWireProperties.expectedWireDate,
      finalWireDate: mockWireProperties.finalWireDate,
      expectedWireAmount: mockWireProperties.expectedWireAmount,
      finalWireAmount: mockWireProperties.finalWireAmount,
      customerId: mockWireProperties.customerId,
      settlementId: mockWireProperties.settlementId,
      frequencyId: mockWireProperties.frequencyId,
      isHold: mockWireProperties.isHold,
      reference: mockWireProperties.reference,
    };
    mockPrisma.wire.create.mockResolvedValue(mockWireResponse);

    await createWireRecord(
      mockPrisma as unknown as PrismaClient,
      propertiesWithoutPaymentRailId
    );

    expect(mockPrisma.wire.create).toHaveBeenCalledWith({
      data: {
        expectedWireDate: propertiesWithoutPaymentRailId.expectedWireDate,
        finalWireDate: propertiesWithoutPaymentRailId.finalWireDate,
        expectedWireAmount: propertiesWithoutPaymentRailId.expectedWireAmount,
        finalWireAmount: propertiesWithoutPaymentRailId.finalWireAmount,
        customerId: propertiesWithoutPaymentRailId.customerId,
        settlementId: propertiesWithoutPaymentRailId.settlementId,
        frequencyId: propertiesWithoutPaymentRailId.frequencyId,
        isHold: propertiesWithoutPaymentRailId.isHold,
        reference: propertiesWithoutPaymentRailId.reference,
        paymentRailId: null,
      },
    });
  });

  it("should create a wire record with both reference and paymentRailId as null when both are not provided", async () => {
    const propertiesWithoutOptionalFields: CreateWireRecordProperties = {
      expectedWireDate: mockWireProperties.expectedWireDate,
      finalWireDate: mockWireProperties.finalWireDate,
      expectedWireAmount: mockWireProperties.expectedWireAmount,
      finalWireAmount: mockWireProperties.finalWireAmount,
      customerId: mockWireProperties.customerId,
      settlementId: mockWireProperties.settlementId,
      frequencyId: mockWireProperties.frequencyId,
      isHold: mockWireProperties.isHold,
    };
    mockPrisma.wire.create.mockResolvedValue(mockWireResponse);

    await createWireRecord(
      mockPrisma as unknown as PrismaClient,
      propertiesWithoutOptionalFields
    );

    expect(mockPrisma.wire.create).toHaveBeenCalledWith({
      data: {
        expectedWireDate: propertiesWithoutOptionalFields.expectedWireDate,
        finalWireDate: propertiesWithoutOptionalFields.finalWireDate,
        expectedWireAmount: propertiesWithoutOptionalFields.expectedWireAmount,
        finalWireAmount: propertiesWithoutOptionalFields.finalWireAmount,
        customerId: propertiesWithoutOptionalFields.customerId,
        settlementId: propertiesWithoutOptionalFields.settlementId,
        frequencyId: propertiesWithoutOptionalFields.frequencyId,
        isHold: propertiesWithoutOptionalFields.isHold,
        reference: null,
        paymentRailId: null,
      },
    });
  });

  it("should create a wire record with isHold set to true", async () => {
    const propertiesWithHold: CreateWireRecordProperties = {
      ...mockWireProperties,
      isHold: true,
    };
    const mockWireResponseWithHold: Wire = {
      ...mockWireResponse,
      isHold: true,
    };
    mockPrisma.wire.create.mockResolvedValue(mockWireResponseWithHold);

    const result = await createWireRecord(
      mockPrisma as unknown as PrismaClient,
      propertiesWithHold
    );

    expect(mockPrisma.wire.create).toHaveBeenCalledWith({
      data: {
        expectedWireDate: propertiesWithHold.expectedWireDate,
        finalWireDate: propertiesWithHold.finalWireDate,
        expectedWireAmount: propertiesWithHold.expectedWireAmount,
        finalWireAmount: propertiesWithHold.finalWireAmount,
        customerId: propertiesWithHold.customerId,
        settlementId: propertiesWithHold.settlementId,
        frequencyId: propertiesWithHold.frequencyId,
        isHold: true,
        reference: propertiesWithHold.reference,
        paymentRailId: propertiesWithHold.paymentRailId,
      },
    });
    expect(result.isHold).toBe(true);
  });

  it("should handle different wire amounts correctly", async () => {
    const propertiesWithDifferentAmounts: CreateWireRecordProperties = {
      ...mockWireProperties,
      expectedWireAmount: 500.75,
      finalWireAmount: 498.25,
    };
    const mockWireResponseWithDifferentAmounts: Wire = {
      ...mockWireResponse,
      expectedWireAmount: 500.75,
      finalWireAmount: 498.25,
    };
    mockPrisma.wire.create.mockResolvedValue(
      mockWireResponseWithDifferentAmounts
    );

    const result = await createWireRecord(
      mockPrisma as unknown as PrismaClient,
      propertiesWithDifferentAmounts
    );

    expect(result.expectedWireAmount).toBe(500.75);
    expect(result.finalWireAmount).toBe(498.25);
  });

  it("should handle frequencyId as optional field", async () => {
    const propertiesWithoutFrequencyId: CreateWireRecordProperties = {
      expectedWireDate: mockWireProperties.expectedWireDate,
      finalWireDate: mockWireProperties.finalWireDate,
      expectedWireAmount: mockWireProperties.expectedWireAmount,
      finalWireAmount: mockWireProperties.finalWireAmount,
      customerId: mockWireProperties.customerId,
      settlementId: mockWireProperties.settlementId,
      isHold: mockWireProperties.isHold,
      reference: mockWireProperties.reference,
      paymentRailId: mockWireProperties.paymentRailId,
    };
    mockPrisma.wire.create.mockResolvedValue(mockWireResponse);

    await createWireRecord(
      mockPrisma as unknown as PrismaClient,
      propertiesWithoutFrequencyId
    );

    expect(mockPrisma.wire.create).toHaveBeenCalledWith({
      data: {
        expectedWireDate: propertiesWithoutFrequencyId.expectedWireDate,
        finalWireDate: propertiesWithoutFrequencyId.finalWireDate,
        expectedWireAmount: propertiesWithoutFrequencyId.expectedWireAmount,
        finalWireAmount: propertiesWithoutFrequencyId.finalWireAmount,
        customerId: propertiesWithoutFrequencyId.customerId,
        settlementId: propertiesWithoutFrequencyId.settlementId,
        frequencyId: undefined,
        isHold: propertiesWithoutFrequencyId.isHold,
        reference: propertiesWithoutFrequencyId.reference,
        paymentRailId: propertiesWithoutFrequencyId.paymentRailId,
      },
    });
  });

  it("should throw an error when Prisma operation fails", async () => {
    const prismaError = new Error("Database connection failed");
    mockPrisma.wire.create.mockRejectedValue(prismaError);

    await expect(
      createWireRecord(
        mockPrisma as unknown as PrismaClient,
        mockWireProperties
      )
    ).rejects.toThrow("Database connection failed");
  });

  it("should properly type cast the Prisma response to Wire type", async () => {
    const prismaResponse = {
      ...mockWireResponse,

      _count: { someRelation: 5 },
    };
    mockPrisma.wire.create.mockResolvedValue(prismaResponse);

    const result = await createWireRecord(
      mockPrisma as unknown as PrismaClient,
      mockWireProperties
    );

    expect(result).toEqual(prismaResponse);

    expect(typeof result.wireId).toBe("number");
    expect(result.expectedWireDate).toBeInstanceOf(Date);
    expect(result.finalWireDate).toBeInstanceOf(Date);
  });
});
