import { type ApprovalCustomerSettlement } from "@lib/customer-settlements/read/type";

export type CreateWireProperties = {
  settlement: ApprovalCustomerSettlement;
  wireAmount: number;
  toDate: string;
};

export type CreateWireRecordProperties = {
  expectedWireDate: Date;
  finalWireDate: Date;
  expectedWireAmount: number;
  finalWireAmount: number;
  customerId: number;
  settlementId: number;
  frequencyId?: number | undefined;
  isHold: boolean;
  reference?: string | undefined;
  paymentRailId?: number | undefined;
};

export type Wire = {
  wireId: number;
  expectedWireDate: Date;
  finalWireDate: Date;
  expectedWireAmount: number;
  finalWireAmount: number;
  isHold?: boolean;
  reference: string;
  frequencyId: number;
  isCancelled: boolean;
  cancelledById?: number;
  cancelledAt?: string;
  customerId: number;
  generatedBeneficiaryId?: number;
  settlementId: number;
  paymentRailId: number;
  customerWireInOutId?: number;
  wirePendingUpdatedById?: number;
  wirePendingUpdatedAt?: string;
  wireApprovedById?: number;
  wireApprovedAt?: string;
  wireGeneratedById?: number;
  wireGeneratedAt?: string;
  journalCreatedById?: number;
  journalCreatedAt?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
};
