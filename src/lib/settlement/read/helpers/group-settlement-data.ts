/* eslint-disable @typescript-eslint/naming-convention */

import { calculatePayouts } from "@lib/settlement/read/helpers/calculate-totals";
import {
  type AdjustmentDetails,
  type PlatformCode,
} from "@lib/settlement/repository/types";
import { type Prisma } from "@prisma/client";
import { objectValuesAreNotNullOrZeroOrUndefined } from "@utils/object-values-are-not-null-or-zero-or-undefined";
import { format } from "date-fns";

import { getSettlementStateAndMessage } from "./get-settlement-state-and-message";
import { type GroupedSettlements } from "../types";

export async function groupSettlementData(
  tx: Prisma.TransactionClient,
  groupData: GroupedSettlements
): Promise<GroupedSettlements> {
  const settlements = await tx.customerSettlements.findMany({
    where: {
      customerId: groupData.customerId,
      fromDate: groupData.fromDate,
      toDate: groupData.toDate,
    },
    select: {
      fromDate: true,
      toDate: true,
      customerSettlementsId: true,
      customerId: true,
      transactionCount: true,
      totalTransactionAmount: true,
      refundCount: true,
      totalRefundAmount: true,
      gatewayFee: true,
      transactionFee: true,
      salesFee: true,
      refundFee: true,
      totalFailedAmount: true,
      total2FaRejectAmount: true,
      total2FaRejectCount: true,
      txnAmountRTO_R: true,
      txnCountETI_R1: true,
      minimumFeeTotal: true,
      minimumFeeCount: true,
      totalMinimumAmount: true,
      partialReturnAmountRTO: true,
      partialReturnCountRTO: true,
      platform: {
        select: {
          platformCode: true,
        },
      },
      customerSettlementAdjustments: {
        select: {
          amount: true,
        },
        where: {
          deletedAt: null,
        },
      },
      customer: {
        select: {
          customerName: true,
          serviceNumber: true,
          enabled: true,
        },
      },
      customerCustomerType: {
        select: {
          customerCustomerTypeId: true,
          customerType: {
            select: {
              customerTypeName: true,
            },
          },
        },
      },
    },
  });

  const processedSettlements = await Promise.all(
    settlements.map(async (settlement) => {
      if (groupData.summary.platformSettlements === undefined) {
        groupData.summary.platformSettlements = {};
      }

      const { platformCode } = settlement.platform;
      const customerTypeName =
        settlement.customerCustomerType?.customerType?.customerTypeName ??
        "Unknown";

      // Determine if adjustments or meaningful values exist
      const platformIsNotZero = objectValuesAreNotNullOrZeroOrUndefined(
        settlement,
        [
          "transactionCount",
          "totalTransactionAmount",
          "refundCount",
          "totalRefundAmount",
          "gatewayFee",
          "transactionFee",
          "salesFee",
          "refundFee",
          "totalFailedAmount",
          "total2FaRejectAmount",
          "total2FaRejectCount",
          "txnAmountRTO_R",
          "txnCountETI_R1",
          "minimumFeeTotal",
          "minimumFeeCount",
          "totalMinimumAmount",
          "partialReturnAmountRTO",
          "partialReturnCountRTO",
        ]
      );

      const adjustments = settlement.customerSettlementAdjustments.map(
        (adj) => ({ amount: Number(adj.amount) })
      );

      if (platformCode === "SUMMARY") {
        const { fromDate, toDate } = settlement;
        const { state, message } = await getSettlementStateAndMessage(
          {
            customerCustomerTypeId:
              settlement.customerCustomerType?.customerCustomerTypeId,
            fromDate: settlement.fromDate,
            toDate: settlement.toDate,
          },
          tx
        );

        // Update summary fields
        groupData.summary.id = String(settlement.customerSettlementsId);
        groupData.summary.customerName = settlement.customer.customerName;
        groupData.summary.customerCustomerTypeId =
          settlement.customerCustomerType?.customerCustomerTypeId ?? 0;
        groupData.summary.serviceNumber = settlement.customer.serviceNumber;
        groupData.summary.customerType = customerTypeName;
        groupData.summary.status = state;
        groupData.summary.netPayout = "0";
        groupData.summary.fromDate = format(fromDate, "yyyy-MM-dd");
        groupData.summary.toDate = format(toDate, "yyyy-MM-dd");

        if (message) {
          groupData.summary.error = message;
        }
      }

      let adjustmentFound = -1;

      if (settlement.customerSettlementAdjustments.length > 0) {
        adjustmentFound = settlement.customerSettlementAdjustments.findIndex(
          (adj) => Number(adj.amount) && { amount: Number(adj.amount) }
        );
      }

      groupData.summary.platformSettlements[platformCode as PlatformCode] = {
        isNonZero: platformIsNotZero,
        isAdjusted: adjustmentFound !== -1,
      };

      return {
        platformCode: platformCode as PlatformCode,
        totalTransactionAmount: Number(settlement.totalTransactionAmount),
        totalFailedAmount: Number(settlement.totalFailedAmount),
        transactionFee: Number(settlement.transactionFee),
        salesFee: Number(settlement.salesFee),
        minimumFeeTotal: Number(settlement.minimumFeeTotal),
        gatewayFee: Number(settlement.gatewayFee),
        refundFee: Number(settlement.refundFee),
        totalRefundAmount: Number(settlement.totalRefundAmount),
        adjustments: adjustments as AdjustmentDetails[],
        customerTypeName,
        isSummary: platformCode === "SUMMARY",
      };
    })
  );

  // Extract customerTypeName from SUMMARY settlement
  const summarySettlement = processedSettlements.find((s) => s.isSummary);
  const customerTypeName = summarySettlement?.customerTypeName ?? "Unknown";

  const { netPayout } = await calculatePayouts(
    customerTypeName,
    processedSettlements
  );

  groupData.summary.netPayout = netPayout.toString();

  return groupData;
}
