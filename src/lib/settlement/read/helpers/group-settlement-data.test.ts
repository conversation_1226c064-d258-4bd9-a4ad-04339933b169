/* eslint-disable @typescript-eslint/naming-convention */
import { Prisma } from "@prisma/client";

import { groupSettlementData } from "./group-settlement-data";

vi.mock("./get-settlement-state-and-message", () => ({
  getSettlementStateAndMessage: vi.fn().mockReturnValue({
    state: "COMPLETED",
    message: "Settlement completed successfully",
  }),
}));
vi.mock("./get-settlement-folder-and-if-exists", () => ({
  getSettlementFolderAndIfExists: vi.fn().mockResolvedValue({
    settlementFolderLocation: "/settlements/2023/01",
    settlementFolderExists: true,
  }),
}));

vi.mock("@lib/settlement/read/helpers/calculate-totals", () => ({
  calculatePayouts: vi.fn().mockReturnValue({
    totalPayout: 600,
    netPayout: 500,
    platformTotals: {
      PLATFORM_X: {
        totalPayable: 700,
      },
    },
  }),
}));

describe("groupSettlementData", () => {
  it("should correctly grab all settlement data", async () => {
    const mockPrisma = {
      customerSettlements: {
        findMany: vi.fn().mockResolvedValue([
          {
            customer: {
              customerName: "Test Customer",
              serviceNumber: "1234567890",
              enabled: true,
            },
            fromDate: new Date("2023-01-01"),
            toDate: new Date("2023-01-31"),
            customerSettlementsId: 1,
            customerId: 123,
            transactionCount: 10,
            totalTransactionAmount: new Prisma.Decimal(1000),
            refundCount: 2,
            totalRefundAmount: new Prisma.Decimal(1000),
            gatewayFee: new Prisma.Decimal(1000),
            transactionFee: new Prisma.Decimal(1000),
            salesFee: new Prisma.Decimal(1000),
            refundFee: new Prisma.Decimal(1000),
            totalFailedAmount: new Prisma.Decimal(1000),
            endBalance: new Prisma.Decimal(1000),
            total2FaRejectAmount: new Prisma.Decimal(1000),
            total2FaRejectCount: 1,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Prisma.Decimal(1000),
            minimumFeeCount: 1,
            totalMinimumAmount: new Prisma.Decimal(1000),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "PLATFORM_X",
              settlementDescription: "Monthly Settlement",
            },
            customerSettlementAdjustments: [
              {
                label: "Adjustment A",
                amount: new Prisma.Decimal(100),
                displayCommentExcel: true,
                comment: "Adjustment for January",
              },
            ],
            customerSettlementKyc: [
              {
                kycType: {
                  kycName: "KYC Type A",
                },
                transactionCount: 5,
                totalTransactionAmount: new Prisma.Decimal(500),
              },
            ],
          },
          {
            customer: {
              customerName: "Test Customer",
              serviceNumber: "1234567890",
              enabled: true,
            },
            fromDate: new Date("2023-01-01"),
            toDate: new Date("2023-01-31"),
            customerSettlementsId: 1,
            customerId: 123,
            transactionCount: 10,
            totalTransactionAmount: new Prisma.Decimal(1000),
            refundCount: 2,
            totalRefundAmount: new Prisma.Decimal(1000),
            gatewayFee: new Prisma.Decimal(1000),
            transactionFee: new Prisma.Decimal(1000),
            salesFee: new Prisma.Decimal(1000),
            refundFee: new Prisma.Decimal(1000),
            totalFailedAmount: new Prisma.Decimal(1000),
            endBalance: new Prisma.Decimal(1000),
            total2FaRejectAmount: new Prisma.Decimal(1000),
            total2FaRejectCount: 1,
            txnAmountRTO_R: 0,
            txnCountETI_R1: 0,
            minimumFeeTotal: new Prisma.Decimal(1000),
            minimumFeeCount: 1,
            totalMinimumAmount: new Prisma.Decimal(1000),
            partialReturnAmountRTO: 0,
            partialReturnCountRTO: 0,
            platform: {
              platformCode: "SUMMARY",
              settlementDescription: "Monthly Settlement",
            },
            customerSettlementAdjustments: [
              {
                label: "Adjustment A",
                amount: new Prisma.Decimal(100),
                displayCommentExcel: true,
                comment: "Adjustment for January",
              },
            ],
            customerSettlementKyc: [
              {
                kycType: {
                  kycName: "KYC Type A",
                },
                transactionCount: 5,
                totalTransactionAmount: new Prisma.Decimal(500),
              },
            ],
          },
        ]),
      },
    };
    const mockSettlementInfo = {
      customerId: 123,
      fromDate: new Date("2023-01-01"),
      toDate: new Date("2023-01-31"),
    };

    // @ts-expect-error mocking prisma
    const result = await groupSettlementData(mockPrisma, mockSettlementInfo);
    expect(result).toEqual({
      id: "1",
      customerName: "Test Customer",
      serviceNumber: "1234567890",
      customerType: "Unknown",
      status: "COMPLETED",
      fromDate: "2023-01-01",
      toDate: "2023-01-31",
      settlementFolderLocation: "/settlements/2023/01",
      error: "Settlement completed successfully",
      platformSettlements: {
        PLATFORM_X: {
          isNonZero: true,
          labelName: "Monthly Settlement",
          transactionCount: 10,
          totalTransactionAmount: 1000,
          refundCount: 2,
          totalRefundAmount: 1000,
          gatewayFee: 1000,
          transactionFee: 1000,
          salesFee: 1000,
          refundFee: 1000,
          totalFailedAmount: 1000,
          endBalance: 1000,
          total2FaRejectAmount: 1000,
          total2FaRejectCount: 1,
          txnAmountRTO_R: 0,
          txnCountETI_R1: 0,
          minimumFeeTotal: 1000,
          minimumFeeCount: 1,
          totalMinimumAmount: 1000,
          partialReturnAmountRTO: 0,
          partialReturnCountRTO: 0,
          isAdjusted: true,
          adjustments: [
            {
              label: "Adjustment A",
              amount: 100,
              displayCommentExcel: true,
              comment: "Adjustment for January",
            },
          ],
          kycDetails: {
            "KYC Type A": {
              transactionCount: 5,
              totalTransactionAmount: 500,
            },
          },
          totalPayable: 700,
        },
        SUMMARY: {
          isNonZero: true,
          labelName: "Monthly Settlement",
          transactionCount: 10,
          totalTransactionAmount: 1000,
          refundCount: 2,
          totalRefundAmount: 1000,
          gatewayFee: 1000,
          transactionFee: 1000,
          salesFee: 1000,
          refundFee: 1000,
          totalFailedAmount: 1000,
          endBalance: 1000,
          total2FaRejectAmount: 1000,
          total2FaRejectCount: 1,
          txnAmountRTO_R: 0,
          txnCountETI_R1: 0,
          minimumFeeTotal: 1000,
          minimumFeeCount: 1,
          totalMinimumAmount: 1000,
          partialReturnAmountRTO: 0,
          partialReturnCountRTO: 0,
          isAdjusted: true,
          adjustments: [
            {
              label: "Adjustment A",
              amount: 100,
              displayCommentExcel: true,
              comment: "Adjustment for January",
            },
          ],
          kycDetails: {
            "KYC Type A": {
              transactionCount: 5,
              totalTransactionAmount: 500,
            },
          },
          totalPayout: 600,
          netPayout: 500,
        },
      },
    });
  });
});
