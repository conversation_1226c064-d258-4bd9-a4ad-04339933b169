import { vi, describe, it, expect, afterEach } from "vitest";

import { handler } from "./handler";
import {
  getSettlementPreferencesService,
  defaultSettlementPreferences,
} from "./service";
import { type SettlementFilters } from "../types";

import type { FastifyReply, FastifyRequest } from "fastify";

vi.mock("./service", () => ({
  getSettlementPreferencesService: vi.fn(),
  defaultSettlementPreferences: {
    filters: {
      textInputValue: "",
      clientType: "",
      displayAdjusted: "Show All",
      state: "",
      status: "No Filter",
      frequency: "",
      startDate: "",
      endDate: "",
    },
    sortKey: "",
    sortOrder: "asc",
    pageNumber: 1,
    recordsPerPage: 20,
  },
}));

describe("Settlement Filter Preferences Handler", () => {
  const mockPrisma = {};
  const mockUserId = 123;

  const mockReply = {
    code: vi.fn(() => mockReply),
    send: vi.fn(),
  } as unknown as FastifyReply;

  const mockRequestBase = {
    server: { prisma: mockPrisma },
    userProfile: { id: mockUserId },
    log: { error: vi.fn() },
  };

  const mockGetRequest = {
    ...mockRequestBase,
    method: "GET",
  } as unknown as FastifyRequest;

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("GET request (fetch preferences)", () => {
    it("should return user preferences when they exist", async () => {
      const mockPreference: SettlementFilters = {
        filters: {
          textInputValue: "existing search",
          clientType: "Agent",
          displayAdjusted: "Show All",
          state: "Approval Success",
          status: "Settlement Approval",
          frequency: "Weekly",
          startDate: "2023-12-01",
          endDate: "2023-12-31",
        },
        sortKey: "fromDate",
        sortOrder: "asc",
        pageNumber: 3,
        recordsPerPage: 25,
      };
      vi.mocked(getSettlementPreferencesService).mockResolvedValue(
        mockPreference
      );

      await handler(mockGetRequest, mockReply);

      expect(getSettlementPreferencesService).toHaveBeenCalledWith(
        mockUserId,
        mockPrisma
      );
      expect(mockReply.code).toHaveBeenCalledWith(200);
      expect(mockReply.send).toHaveBeenCalledWith(mockPreference);
    });

    it("should return default preferences when user has no saved preferences", async () => {
      vi.mocked(getSettlementPreferencesService).mockResolvedValue(
        defaultSettlementPreferences
      );

      await handler(mockGetRequest, mockReply);

      expect(getSettlementPreferencesService).toHaveBeenCalledWith(
        mockUserId,
        mockPrisma
      );
      expect(mockReply.code).toHaveBeenCalledWith(200);
      expect(mockReply.send).toHaveBeenCalledWith(defaultSettlementPreferences);
    });
  });
});
