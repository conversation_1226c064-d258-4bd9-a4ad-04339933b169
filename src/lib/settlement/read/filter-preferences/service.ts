import { getSettlementPreference } from "./repository";
import { type SettlementFilters } from "../types";

import type { PrismaClient } from "@prisma/client";

export const defaultSettlementPreferences: SettlementFilters = {
  filters: {
    textInputValue: "",
    clientType: "",
    displayAdjusted: "Show All",
    state: "",
    status: "No Filter",
    frequency: "",
    startDate: undefined,
    endDate: undefined,
  },
  sortKey: "fromDate",
  sortOrder: "desc",
  pageNumber: 1,
  recordsPerPage: 20,
} as const;

export const getSettlementPreferencesService = async (
  userId: number,
  prisma: PrismaClient
): Promise<SettlementFilters> => {
  const preferences = await getSettlementPreference(userId, prisma);

  return preferences ?? defaultSettlementPreferences;
};
