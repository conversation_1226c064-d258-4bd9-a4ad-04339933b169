import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Fetch Settlements",
  description: "Fetches the setttlements from the given page.",
  tags: [tags.build],
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        totalCount: {
          type: "integer",
          description: "Total number of settlements",
        },
        settlements: {
          type: "array",
          items: {
            customerId: {
              type: "integer",
              description: "ID of the customer",
            },
            fromDate: {
              type: "string",
              description: "Start date of the settlement period",
            },
            summary: {
              id: {
                type: "integer",
                description: "ID of the settlement",
              },
              customerName: {
                type: "string",
                description: "Name of the customer",
              },
              serviceNumber: {
                type: "string",
                description: "Service number of the customer",
              },
              customerType: {
                type: "string",
                description: "Type of the customer",
              },
              status: {
                type: "string",
                description: "Status of the settlement",
              },
              netPayout: {
                type: "string",
                description: "Net payout amount",
              },
              endBalance: {
                type: "string",
                description: "End balance amount",
              },
              fromDate: {
                type: "string",
                description: "Start date of the settlement period",
              },
              toDate: {
                type: "string",
                description: "End date of the settlement period",
              },
              platformSettlements: {
                type: "object",
                additionalProperties: {
                  type: "object",
                  properties: {
                    isAdjusted: {
                      type: "boolean",
                      description: "Indicates if the settlement is adjusted",
                    },
                    isNonZero: {
                      type: "boolean",
                      description:
                        "Indicates if the settlement has non-zero values",
                    },
                  },
                },
              },
              customerCustomerTypeId: {
                type: "number",
                description: "Type id of Customer",
              },
              error: {
                type: "string",
                description: "Error message if any",
              },
            },
          },
          description: "List of settlements",
          required: [
            "customerId",
            "summary",
            "id",
            "customerName",
            "serviceNumber",
            "customerType",
            "status",
            "netPayout",
            "endBalance",
            "fromDate",
            "toDate",
            "platformSettlements",
            "customerCustomerTypeId",
            "isAdjusted",
          ],
        },
      },
      required: ["totalCount", "settlements"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Error response",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
  },
};
