import { getSettlements } from "./get-settlements";
import * as GetGroupedCustomerSettlementsBySort from "./helpers/get-grouped-customer-settlements-by-sort";
import * as GetGroupedSettlementsTotalCount from "./helpers/get-grouped-settlements-count";
import * as GroupSettlementData from "./helpers/group-settlement-data";

import type { PrismaClient } from "@prisma/client";

describe("getSettlements", () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should return settlements and total count", async () => {
    const mockPrismaFunctions = {
      customerSettlements: {
        findMany: vi.fn(),
      },
    };

    const mockPrisma = {
      $transaction: vi
        .fn()
        // eslint-disable-next-line max-len
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call
        .mockImplementation((callback) => callback(mockPrismaFunctions)),
    } as unknown as PrismaClient;

    const offset = 0;
    const limit = 10;
    const sortKey = "fromDate";
    const sortOrder = "asc";

    const spyGetGroupedCustomerSettlementsBySort = vi
      .spyOn(
        GetGroupedCustomerSettlementsBySort,
        "getGroupedCustomerSettlementsBySort"
      )
      .mockResolvedValue([
        {
          customerId: 1,
          fromDate: new Date("2025-01-01"),
          toDate: new Date("2025-01-02"),
        },
      ]);

    const spyGroupSettlementData = vi
      .spyOn(GroupSettlementData, "groupSettlementData")
      .mockImplementation(async () => {
        return {
          id: "123",
          customerName: "test",
          serviceNumber: "12345",
          customerType: "Merchant",
          status: "Skipped",
          netPayout: "0",
          endBalance: "111.11",
          fromDate: "2025-01-01",
          toDate: "2025-01-02",
          settlementFolderLocation: undefined,
          platformSettlements: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            SUMMARY: {
              isAdjusted: false,
            },
            // eslint-disable-next-line @typescript-eslint/naming-convention
            RTO: {
              isAdjusted: true,
            },
            // eslint-disable-next-line @typescript-eslint/naming-convention
            ETI: {
              isAdjusted: false,
            },
            // eslint-disable-next-line @typescript-eslint/naming-convention
            ACH: {
              isAdjusted: true,
            },
          },
        };
      });

    const spyGetGroupedSettlementsTotalCount = vi
      .spyOn(GetGroupedSettlementsTotalCount, "getGroupedSettlementsTotalCount")
      .mockResolvedValue(1);

    const promiseAllSpy = vi.spyOn(Promise, "all");

    const result = await getSettlements({
      prisma: mockPrisma,
      joinConditions: [],
      havingConditions: [],
      whereConditions: [],
      offset,
      limit,
      sortKey,
      sortOrder,
    });

    expect(spyGetGroupedCustomerSettlementsBySort).toHaveBeenCalledWith({
      tx: mockPrismaFunctions,
      joinConditions: [],
      havingConditions: [],
      whereConditions: [],
      offset,
      limit,
      sortKey,
      sortOrder,
    });

    expect(spyGetGroupedSettlementsTotalCount).toHaveBeenCalledWith(
      mockPrismaFunctions,
      [],
      [],
      []
    );

    expect(spyGroupSettlementData).toHaveBeenCalledTimes(1);

    expect(promiseAllSpy).toHaveBeenCalled();

    expect(result).toEqual({
      totalCount: 1,
      settlements: [
        {
          id: "123",
          customerName: "test",
          serviceNumber: "12345",
          customerType: "Merchant",
          status: "Skipped",
          netPayout: "0",
          endBalance: "111.11",
          fromDate: "2025-01-01",
          toDate: "2025-01-02",
          platformSettlements: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            SUMMARY: {
              isAdjusted: false,
            },
            // eslint-disable-next-line @typescript-eslint/naming-convention
            RTO: {
              isAdjusted: true,
            },
            // eslint-disable-next-line @typescript-eslint/naming-convention
            ETI: {
              isAdjusted: false,
            },
            // eslint-disable-next-line @typescript-eslint/naming-convention
            ACH: {
              isAdjusted: true,
            },
          },
        },
      ],
    });
  });
});
