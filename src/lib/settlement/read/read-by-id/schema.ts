/* eslint-disable @typescript-eslint/naming-convention */
import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

const adjustments = {
  type: "array",
  description: "Adjustments made to the settlement",
  items: {
    type: "object",
    description: "Details of each adjustment",
    properties: {
      label: {
        type: "string",
        description: "Label of the adjustment",
      },
      amount: {
        type: "number",
        description: "Amount adjusted",
      },
      displayCommentExcel: {
        type: "boolean",
        description:
          "Indicates if the comment should be displayed in the Excel file",
      },
      comment: {
        type: "string",
        description: "Comment associated with the adjustment",
      },
    },
  },
};

const kycDetails = {
  type: "object",
  description: "Details of each type of KYC",
  properties: {
    KY1: {
      type: "object",
      properties: {
        transactionCount: {
          type: "number",
          description: "Number of transactions",
        },
        totalTransactionAmount: {
          type: "number",
          description: "Total transaction amount",
        },
      },
    },
    KY2: {
      type: "object",
      properties: {
        transactionCount: {
          type: "number",
          description: "Number of transactions",
        },
        totalTransactionAmount: {
          type: "number",
          description: "Total transaction amount",
        },
      },
    },
    KY3: {
      type: "object",
      properties: {
        transactionCount: {
          type: "number",
          description: "Number of transactions",
        },
        totalTransactionAmount: {
          type: "number",
          description: "Total transaction amount",
        },
      },
    },
    KY4: {
      type: "object",
      properties: {
        transactionCount: {
          type: "number",
          description: "Number of transactions",
        },
        totalTransactionAmount: {
          type: "number",
          description: "Total transaction amount",
        },
      },
    },
    KY5: {
      type: "object",
      properties: {
        transactionCount: {
          type: "number",
          description: "Number of transactions",
        },
        totalTransactionAmount: {
          type: "number",
          description: "Total transaction amount",
        },
      },
    },
    KY6: {
      type: "object",
      properties: {
        transactionCount: {
          type: "number",
          description: "Number of transactions",
        },
        totalTransactionAmount: {
          type: "number",
          description: "Total transaction amount",
        },
      },
    },
  },
};

const settlementDetails = {
  type: "object",
  properties: {
    labelName: {
      type: "string",
      description: "Label name of the platform settlement",
    },
    isNonZero: {
      type: "boolean",
      description: "Indicates if the settlement has non-zero values",
    },
    transactionCount: { type: "number", description: "Number of transactions" },
    totalTransactionAmount: {
      type: "number",
      description: "Total transaction amount",
    },
    refundCount: { type: "number", description: "Number of refunds" },
    totalRefundAmount: { type: "number", description: "Total refund amount" },
    gatewayFee: { type: "number", description: "Gateway fee" },
    transactionFee: { type: "number", description: "Transaction fee" },
    salesFee: { type: "number", description: "Sales fee" },
    refundFee: { type: "number", description: "Refund fee" },
    totalFailedAmount: { type: "number", description: "Total failed amount" },
    endBalance: { type: "number", description: "End balance" },
    total2FaRejectAmount: {
      type: "number",
      description: "Total transaction amount that was rejected by 2FA",
    },
    total2FaRejectCount: {
      type: "number",
      description: "Number of transactions rejected by 2FA",
    },
    txnAmountRTO_R: {
      type: "number",
      description: "Total transaction amount of _R transactions",
    },
    txnCountETI_R1: {
      type: "number",
      description: "Number of _R transactions",
    },
    minimumFeeTotal: { type: "number", description: "Total minimum fee" },
    minimumFeeCount: {
      type: "number",
      description: "Number of minimum fee transactions",
    },
    totalMinimumAmount: {
      type: "number",
      description: "Total minimum fee transaction amount",
    },
    partialReturnAmountRTO: {
      type: "number",
      description: "Total transaction amount of partial return transactions",
    },
    partialReturnCountRTO: {
      type: "number",
      description: "Number of partial return transactions",
    },
    totalPayable: { type: "number", description: "Total payable amount" },
    isAdjusted: {
      type: "boolean",
      description: "Indicates if the settlement is adjusted",
    },
    totalPayout: { type: "number", description: "Total payout amount" },
    totalAdjustments: {
      type: "number",
      description: "Total adjustment amount made to the settlement",
    },
    netPayout: { type: "number", description: "Net payout amount" },
    totalCosts: { type: "number", description: "Total costs in fees" },
    adjustments,
    kycDetails,
  },
};

export const schema: FastifySchema = {
  summary: "Fetch Settlement by ID",
  description: "Fetches a settlement by its ID.",
  tags: [tags.settlement],
  params: {
    type: "object",
    properties: {
      id: { type: "number", description: "ID of the settlement" },
    },
    required: ["id"],
  },
  response: {
    200: {
      description: "Successful response",
      type: "object",
      properties: {
        id: {
          type: "string",
          description: "ID of the customer settlement summary",
        },
        customerName: { type: "string", description: "Name of the customer" },
        serviceNumber: {
          type: "string",
          description: "Service number of the customer",
        },
        customerType: { type: "string", description: "Type of the customer" },
        status: { type: "string", description: "State of the settlement" },
        fromDate: {
          type: "string",
          format: "date-time",
          description: "Start date of the settlement period",
        },
        toDate: {
          type: "string",
          format: "date-time",
          description: "End date of the settlement period",
        },
        platformSettlements: {
          type: "object",
          description: "Each platform's settlement details",
          properties: {
            IDP: settlementDetails,
            ETI: settlementDetails,
            ETF: settlementDetails,
            RFM: settlementDetails,
            ETO: settlementDetails,
            RTO: settlementDetails,
            RTX: settlementDetails,
            ACH: settlementDetails,
            ANR: settlementDetails,
            ANX: settlementDetails,
            KYC: settlementDetails,
            SUMMARY: settlementDetails,
          },
        },
        settlementFolderLocation: {
          type: "string",
          description:
            "Location of the settlement folder, undefined if not available",
        },
      },
    },

    404: {
      description: "Settlement not found",
      type: "object",
      required: ["message"],
      properties: {
        message: {
          type: "string",
          description: "Message indicating that the settlement was not found",
        },
      },
    },

    500: {
      description: "Internal server error",
      type: "object",
      required: ["message"],
      properties: {
        message: {
          type: "string",
          description: "Error message",
        },
      },
    },
  },
};
