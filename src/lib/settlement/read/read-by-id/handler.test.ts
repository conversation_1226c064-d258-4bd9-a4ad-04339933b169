import { getSettlementData } from "./functions/get-settlement-data";
import { handler } from "./handler";

import type { SettlementSummary } from "@lib/settlement/repository/types";
import type { FastifyReply, FastifyRequest } from "fastify";

vi.mock("./functions/get-settlement-data", () => ({
  getSettlementData: vi.fn(),
}));

const mockReply = {
  code: vi.fn(() => mockReply),
  send: vi.fn(),
} as unknown as FastifyReply;

const mockRequest = {
  params: { id: 123 },
  server: { prisma: {} },
  log: { error: vi.fn() },
} as unknown as FastifyRequest;

describe("Read Settlement by ID Handler", () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should return settlement data if found", async () => {
    const settlement: SettlementSummary = {
      id: "1",
      customerName: "Test Customer",
      serviceNumber: "12345",
      customerType: "Residential",
      status: "Processing",
      fromDate: "2023-01-01",
      toDate: "2023-01-31",
      platformSettlements: {},
      settlementFolderLocation: undefined,
    };
    vi.mocked(getSettlementData).mockResolvedValue(settlement);

    await handler(mockRequest, mockReply);

    expect(getSettlementData).toHaveBeenCalledWith(
      123,
      mockRequest.server.prisma
    );
    expect(mockReply.send).toHaveBeenCalledWith(settlement);
    expect(mockReply.code).not.toHaveBeenCalled();
  });

  it("should return 404 if settlement not found", async () => {
    await handler(mockRequest, mockReply);

    expect(getSettlementData).toHaveBeenCalledWith(
      123,
      mockRequest.server.prisma
    );
    expect(mockReply.code).toHaveBeenCalledWith(404);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: "Settlement not found",
    });
  });

  it("should handle errors and return 500", async () => {
    const error = new Error("Database error");
    vi.mocked(getSettlementData).mockRejectedValue(error);

    await handler(mockRequest, mockReply);

    expect(mockRequest.log.error).toHaveBeenCalledWith(error);
    expect(mockReply.code).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({ message: "Database error" });
  });
});
