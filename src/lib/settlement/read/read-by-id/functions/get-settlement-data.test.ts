/* eslint-disable @typescript-eslint/naming-convention */

import { getSettlementById } from "@lib/settlement/repository/settlement-frequency";

import { getSettlementData } from "./get-settlement-data";
import { groupSettlementData } from "../../helpers/group-settlement-data";

vi.mock("@lib/settlement/read/helpers/group-settlement-data", () => ({
  groupSettlementData: vi.fn(),
}));
vi.mock("@lib/settlement/repository/settlement-frequency", () => ({
  getSettlementById: vi.fn(),
}));

describe("getSettlementData", () => {
  it("should return undefined if settlement is not found", async () => {
    // @ts-expect-error mocking prisma type
    const result = await getSettlementData(1, {});

    expect(result).toBeUndefined();
  });

  it("should return settlement data if groupSettlementData returns data", async () => {
    const expectedAndActualResult = {
      id: "1",
      customerName: "Test Customer",
      serviceNumber: "12345",
      customerType: "Merchant",
      status: "COMPLETED",
      fromDate: new Date(),
      toDate: new Date(),
      transactionCount: 10,
      totalTransactionAmount: 1000,
      refundCount: 2,
      totalRefundAmount: 200,
      gatewayFee: 50,
      transactionFee: 30,
      salesFee: 20,
      refundFee: 10,
      totalFailedAmount: 5,
      endBalance: 800,
      total2FaRejectAmount: 15,
      total2FaRejectCount: 3,
      txnAmountRTO_R: 100,
      txnCountETI_R1: 5,
      minimumFeeTotal: 10,
      minimumFeeCount: 1,
      totalMinimumAmount: 50,
      platformSettlements: {},
      settlementFolderLocation: undefined,
    };

    vi.mocked(getSettlementById).mockResolvedValue({
      customerId: 1,
      customerName: "Test Customer",
      serviceNumber: "12345",
      customerType: "Merchant",
      customerCustomerTypeId: 3,
      fromDate: new Date(),
      toDate: new Date(),
    });

    vi.mocked(groupSettlementData).mockResolvedValue(
      // @ts-expect-error mocking the result returned
      expectedAndActualResult
    );

    // @ts-expect-error mocking prisma type
    const result = await getSettlementData(1, {});

    expect(result).toEqual(expectedAndActualResult);
  });

  it("should return nothing if groupSettlementData returns undefined", async () => {
    vi.mocked(getSettlementById).mockResolvedValue({
      customerId: 1,
      customerName: "Test Customer",
      serviceNumber: "12345",
      customerType: "Merchant",
      customerCustomerTypeId: 3,
      fromDate: new Date(),
      toDate: new Date(),
    });

    // @ts-expect-error mocking the result returned
    vi.mocked(groupSettlementData).mockResolvedValue();

    // @ts-expect-error mocking prisma type
    const result = await getSettlementData(1, {});

    expect(result).toBeUndefined();
  });
});
