import { getSettlementById } from "@lib/settlement/repository/settlement-frequency";

import { groupSettlementData } from "../../helpers/group-settlement-data";

import type { SettlementSummary } from "@lib/settlement/repository/types";
import type { PrismaClient } from "@prisma/client";

export const getSettlementData = async (
  id: number,
  prisma: PrismaClient
): Promise<SettlementSummary | undefined> => {
  const settlement = await getSettlementById(id, prisma);

  if (!settlement) {
    return;
  }

  const { customerId, fromDate, toDate } = settlement;

  const result = await groupSettlementData(prisma, {
    customerId,
    fromDate,
    toDate,
  });

  return result;
};
