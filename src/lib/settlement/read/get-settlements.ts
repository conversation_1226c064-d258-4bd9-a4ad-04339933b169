import { getGroupedCustomerSettlementsBySort } from "./helpers/get-grouped-customer-settlements-by-sort";
import { getGroupedSettlementsTotalCount } from "./helpers/get-grouped-settlements-count";
import { groupSettlementData } from "./helpers/group-settlement-data";
import { type GroupedSettlements } from "./types";
import { getAuthToken } from "../../../services/blusky/authentication";
import { getEndBalance } from "../../../services/blusky/end-balance";

import type { Prisma, PrismaClient } from "@prisma/client";

const getSettlementsTimeout = 20_000; // 20 seconds

const getSettlements = async ({
  prisma,
  joinConditions,
  whereConditions,
  havingConditions,
  offset,
  limit,
  sortKey,
  sortOrder,
}: {
  prisma: PrismaClient;
  joinConditions: Prisma.Sql[];
  whereConditions: Prisma.Sql[];
  havingConditions: Prisma.Sql[];
  offset: number;
  limit: number;
  sortKey: string;
  sortOrder: "asc" | "desc";
}): Promise<{ totalCount: number; settlements: GroupedSettlements[] }> => {
  const allPlatformRecords = await prisma.$transaction(
    async (tx) => {
      let customerSettlementsGrouped = [];

      // Step 1
      // Get the customer settlements grouped by customerId and fromDate
      // in their sorted order
      customerSettlementsGrouped = await getGroupedCustomerSettlementsBySort({
        tx,
        joinConditions,
        whereConditions,
        havingConditions,
        offset,
        limit,
        sortKey,
        sortOrder,
      });

      // Step 2
      // Create an array of GroupedSettlements objects that contains the data we will return
      let customerSettlementsGroupedData: GroupedSettlements[] =
        customerSettlementsGrouped.map((group) => ({
          customerId: group.customerId,
          fromDate: group.fromDate,
          toDate: group.toDate,
          summary: {
            id: "",
            customerName: "",
            serviceNumber: "",
            customerType: "",
            status: "Skipped",
            netPayout: "0",
            endBalance: "0",
            fromDate: ``,
            toDate: ``,
            platformSettlements: {},
            customerCustomerTypeId: 0,
          },
        }));

      // Step 3
      // Get the total count of customer settlements for the pagination
      const totalCount = await getGroupedSettlementsTotalCount(
        tx,
        whereConditions,
        joinConditions,
        havingConditions
      );

      const settlementSummaryPromises = [];

      // Step 4
      // Get the X number of settlements from the DB for one settlement record
      for (const groupData of customerSettlementsGroupedData) {
        const promise = groupSettlementData(tx, groupData).then((result) => {
          return result;
        });

        settlementSummaryPromises.push(promise);
      }

      // Step 5
      // Wait till all settlementSummaries are done

      customerSettlementsGroupedData = await Promise.all(
        settlementSummaryPromises
      );

      // Because the customer settlements are grouped there must be at least one settlement
      // per group
      const allServiceNumbers = customerSettlementsGroupedData.map(
        (groupData) => groupData.summary.serviceNumber
      );

      let endBalanceMap: Record<
        string,
        Record<
          "customerName" | "endBalance" | "currentBalance" | "totalBalance",
          string | number
        >
      > = {};

      // Step 6
      // Get the end balance for each service number
      // We do this at the end so we can grab all the service numbers
      // and then just make one call to Blusky

      if (allServiceNumbers.length > 0) {
        const bluskyToken = await getAuthToken();
        endBalanceMap = await getEndBalance(allServiceNumbers, bluskyToken);
      }

      for (const groupData of customerSettlementsGroupedData) {
        groupData.summary.endBalance = endBalanceMap[
          groupData.summary.serviceNumber
        ]?.endBalance
          ? String(endBalanceMap[groupData.summary.serviceNumber]?.endBalance)
          : "Not Found";
      }

      return [totalCount, customerSettlementsGroupedData];
    },
    {
      timeout: getSettlementsTimeout,
    }
  );

  return {
    totalCount: allPlatformRecords[0] as number,
    settlements: allPlatformRecords[1] as GroupedSettlements[],
  };
};

export { getSettlements };
