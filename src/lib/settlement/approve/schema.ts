import { tags } from "@constants/schema/tags";

import type { FastifySchema } from "fastify";

export const schema: FastifySchema = {
  summary: "Approve Settlement",
  description: "Approve settlement for a client.",
  tags: [tags.settlement],
  body: {
    type: "object",
    properties: {
      serviceNumber: {
        type: "string",
        description: "A service number of the customer",
      },
      fromDate: {
        type: "string",
        description: "From date of the generated settlement",
      },
      toDate: {
        type: "string",
        description: "To date of the generated settlement",
      },
      customerSettlementsId: {
        type: "number",
        description: "Customer settlements ID",
      },
      customerCustomerTypeId: {
        type: "number",
        description: "Customer customer type ID",
      },
      netPayout: {
        type: "number",
        description: "Net payout of the settlement",
      },
    },
    required: [
      "serviceNumber",
      "fromDate",
      "toDate",
      "customerCustomerTypeId",
      "netPayout",
    ],
  },
  response: {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    200: {
      description: "Successful approve settlement.",
      type: "object",
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    404: {
      description: "Settlement not found.",
      type: "object",
      properties: {
        message: { type: "string" },
      },
      required: ["message"],
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    500: {
      description: "Internal server error during settlement approval.",
      type: "object",
      properties: {
        error: {
          type: "object",
          properties: {
            message: { type: "string" },
          },
          required: ["message"],
        },
      },
      required: ["error"],
    },
  },
};
