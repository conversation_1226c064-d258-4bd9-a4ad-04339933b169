import { createCustomerSettlementGenerationSchedule } from "@lib/customer-settlement-generation-schedule/update/repository";
import { fetchPreviousSettlementForEndBalance } from "@lib/customer-settlements/read/repository";
import { getSettlementForApproval } from "@lib/customer-settlements/read/service";
import {
  type ApprovalCustomerSettlement,
  type PreviousSettlement,
} from "@lib/customer-settlements/read/type";
import {
  updateCustomerSettlementsToApproved,
  updateCustomerSettlementWithEndBalance,
} from "@lib/customer-settlements/update/repository";
import { getWirePayments } from "@lib/customer-wire-in-outs/repository";
import { type WirePayment } from "@lib/customer-wire-in-outs/type";
import { createEmail } from "@lib/email/create/service";
import { type ApproveParameters } from "@lib/settlement/approve/types";
import { createWire } from "@lib/wire/create/service";
import { toDateOnlyFromString } from "@utils/date-only";
import { subDays } from "date-fns";

import ExcelJS from "exceljs";

import type { PrismaClient } from "@prisma/client";
import { getAllSettlementsByCustomerAndPeriod } from "../repository/settlement-frequency";

export const approveSettlement = async (
  prisma: PrismaClient,
  approveParameters: ApproveParameters
) => {
  const {
    serviceNumber,
    fromDate,
    toDate,
    customerSettlementsId,
    customerCustomerTypeId,
  } = approveParameters;

  try {
    // await createCustomerSettlementGenerationSchedule(prisma, {
    //   serviceNumber,
    //   fromDate,
    //   toDate,
    //   customerCustomerTypeId,
    //   generationType: "FINAL",
    //   generationStatus: "PENDING",
    // });

    // await createCustomerSettlementGenerationSchedule(prisma, {
    //   serviceNumber,
    //   fromDate,
    //   toDate,
    //   customerCustomerTypeId,
    //   generationType: "FINAL",
    //   generationStatus: "STARTED",
    // });

    const settlement = await getSettlementForApproval(
      prisma,
      customerSettlementsId
    );

    const customerType =
      settlement.customerCustomerType.customerType.customerTypeName;

    await (customerType === "Merchant"
      ? approveMerchantSettlement(prisma, settlement, approveParameters)
      : approveNonMerchantSettlement());

    const scheduleRecord = await createCustomerSettlementGenerationSchedule(
      prisma,
      {
        serviceNumber,
        fromDate,
        toDate,
        customerCustomerTypeId,
        generationType: "FINAL",
        generationStatus: "COMPLETE",
        completionDate: new Date(),
      }
    );

    return {
      success: true,
      message: "Settlement approved successfully",
      scheduleId: scheduleRecord.customerSettlementGenerationScheduleId,
    };
  } catch (error) {
    await createCustomerSettlementGenerationSchedule(prisma, {
      serviceNumber,
      fromDate,
      toDate,
      customerCustomerTypeId,
      generationType: "FINAL",
      generationStatus: "ERROR",
      errorMessage: (error as Error).message,
    });

    throw error;
  }
};

const generateExel = async (settlement, prisma) => {
  const settlements = await getAllSettlementsByCustomerAndPeriod(
    settlement.customerId,
    settlement.fromDate,
    settlement.toDate,
    prisma
  );
  const workbook = new ExcelJS.Workbook();
  console.log(11111111, JSON.stringify(settlements));

  // RESPONSE settlements
  // [
  //   {
  //     SUMMARY: {
  //       labelName: null,
  //       transactionCount: 0,
  //       totalTransactionAmount: 0,
  //       refundCount: 0,
  //       totalRefundAmount: 0,
  //       gatewayFee: 0,
  //       transactionFee: 0,
  //       salesFee: 0,
  //       refundFee: 0,
  //       totalFailedAmount: 0,
  //       endBalance: 0,
  //       total2FaRejectAmount: 0,
  //       total2FaRejectCount: 0,
  //       txnAmountRTO_R: 0,
  //       txnCountETI_R1: 0,
  //       minimumFeeTotal: 0,
  //       minimumFeeCount: 0,
  //       totalMinimumAmount: 0,
  //       partialReturnAmountRTO: 0,
  //       partialReturnCountRTO: 0,
  //       isAdjusted: false,
  //       adjustments: [],
  //     },
  //   },
  //   {
  //     ETI: {
  //       labelName: "Interac e-Transfer Pay-In (ETI)",
  //       transactionCount: 203,
  //       totalTransactionAmount: 20849.11,
  //       refundCount: 0,
  //       totalRefundAmount: 0,
  //       gatewayFee: 0,
  //       transactionFee: 111.65,
  //       salesFee: 475.36,
  //       refundFee: 0,
  //       totalFailedAmount: 0,
  //       endBalance: 0,
  //       total2FaRejectAmount: 0,
  //       total2FaRejectCount: 0,
  //       txnAmountRTO_R: 0,
  //       txnCountETI_R1: 0,
  //       minimumFeeTotal: 0,
  //       minimumFeeCount: 0,
  //       totalMinimumAmount: 0,
  //       partialReturnAmountRTO: 0,
  //       partialReturnCountRTO: 0,
  //       isAdjusted: false,
  //       adjustments: [],
  //     },
  //   },
  //   {
  //     RFM: {
  //       labelName: "Interac e-Transfer Request Money (RFM)",
  //       transactionCount: 143,
  //       totalTransactionAmount: 25902.31,
  //       refundCount: 0,
  //       totalRefundAmount: 0,
  //       gatewayFee: 0,
  //       transactionFee: 78.65,
  //       salesFee: 590.57,
  //       refundFee: 0,
  //       totalFailedAmount: 0,
  //       endBalance: 0,
  //       total2FaRejectAmount: 0,
  //       total2FaRejectCount: 0,
  //       txnAmountRTO_R: 0,
  //       txnCountETI_R1: 0,
  //       minimumFeeTotal: 0,
  //       minimumFeeCount: 0,
  //       totalMinimumAmount: 0,
  //       partialReturnAmountRTO: 0,
  //       partialReturnCountRTO: 0,
  //       isAdjusted: false,
  //       adjustments: [],
  //     },
  //   },
  //   {
  //     KYC: {
  //       labelName: "KYC",
  //       transactionCount: 0,
  //       totalTransactionAmount: 0,
  //       refundCount: 0,
  //       totalRefundAmount: 0,
  //       gatewayFee: 0,
  //       transactionFee: 0,
  //       salesFee: 0,
  //       refundFee: 0,
  //       totalFailedAmount: 0,
  //       endBalance: 0,
  //       total2FaRejectAmount: 0,
  //       total2FaRejectCount: 0,
  //       txnAmountRTO_R: 0,
  //       txnCountETI_R1: 0,
  //       minimumFeeTotal: 0,
  //       minimumFeeCount: 0,
  //       totalMinimumAmount: 0,
  //       partialReturnAmountRTO: 0,
  //       partialReturnCountRTO: 0,
  //       isAdjusted: false,
  //       adjustments: [],
  //     },
  //   },
  // ];
};

export const approveMerchantSettlement = async (
  prisma: PrismaClient,
  settlement: ApprovalCustomerSettlement,
  approveParameters: ApproveParameters
) => {
  const { fromDate, toDate, netPayout, userId } = approveParameters;

  // Convert string dates to DateOnly format - renamed variables to avoid conflict
  const convertedFromDate = toDateOnlyFromString(fromDate);
  const convertedToDate = toDateOnlyFromString(toDate);

  // const previousDate = getPreviousDay(fromDate);

  // const [previousSettlement, wires] = await Promise.all([
  //   fetchPreviousSettlementForEndBalance(
  //     prisma,
  //     settlement.customerId,
  //     settlement.platform.platformId,
  //     previousDate
  //   ),
  //   getWirePayments(
  //     settlement.customerId,
  //     convertedFromDate,
  //     convertedToDate,
  //     prisma
  //   ),
  // ]);

  // const newEndBalance = calculateEndBalance(
  //   wires,
  //   previousSettlement,
  //   netPayout
  // );

  generateExel(settlement, prisma);

  // if (settlement.customerCustomerType.statementFolderLocation) {
  //   const [wire] = await Promise.all([
  //     createWire(prisma, {
  //       settlement,
  //       wireAmount: netPayout,
  //       toDate,
  //     }),
  //     updateCustomerSettlementsToApproved(prisma, {
  //       customerId: settlement.customerId,
  //       fromDate,
  //       toDate,
  //       approvedBy: userId,
  //     }),
  //     updateCustomerSettlementWithEndBalance(
  //       prisma,
  //       settlement.customerSettlementsId,
  //       newEndBalance,
  //       userId
  //     ),
  //   ]);

  //   const emailConfiguration = settlement.customer?.emailConfiguration;

  //   if (!emailConfiguration || emailConfiguration.isEmailEnabled) {
  //     await createEmail(prisma, settlement, wire);
  //   }
  // }
};

const getPreviousDay = (fromDate: string): Date => {
  return subDays(new Date(fromDate), 1);
};

const calculateEndBalance = (
  wires: WirePayment[],
  previousSettlement: PreviousSettlement | undefined,
  netPayout: number
): number => {
  let totalWireAmount = 0;

  for (const wire of wires) {
    if (wire.transactionType === "I") {
      totalWireAmount += Number(wire.transactionAmount);
    } else {
      totalWireAmount -= Number(wire.transactionAmount);
    }
  }

  return (
    Number(previousSettlement?.endBalance ?? 0) +
    Number(totalWireAmount ?? 0) +
    Number(netPayout)
  );
};

export const approveNonMerchantSettlement = async () => {
  throw new Error(`NonMerchant settlement approval is not yet implemented.`);
};
