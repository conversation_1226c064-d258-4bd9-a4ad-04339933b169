import { existsSync, mkdirSync, readFileSync } from "node:fs";
import path from "node:path";

import { baseDirectoryName } from "@constants/filesystem";
import {
  excelNumberFormat,
  type PayInPlatform,
  payInPlatforms,
  summaryColumns,
  transactionColumns,
} from "@constants/settlement";
import { createCustomerSettlementGenerationSchedule } from "@lib/customer-settlement-generation-schedule/update/repository";
import { fetchPreviousSettlementForEndBalance } from "@lib/customer-settlements/read/repository";
import { getSettlementForApproval } from "@lib/customer-settlements/read/service";
import {
  type ApprovalCustomerSettlement,
  type PreviousSettlement,
} from "@lib/customer-settlements/read/type";
import {
  updateCustomerSettlementsToApproved,
  updateCustomerSettlementWithEndBalance,
} from "@lib/customer-settlements/update/repository";
import { getWirePayments } from "@lib/customer-wire-in-outs/repository";
import { type WirePayment } from "@lib/customer-wire-in-outs/type";
import { createEmail } from "@lib/email/create/service";
import { createWire } from "@lib/wire/create/service";
import { toDateOnly, toDateOnlyFromString } from "@utils/date-only";
import { format, subDays } from "date-fns";
import ExcelJS from "exceljs";

import {
  type SettlementExcelData,
  type ApproveParameters,
  type ExcelConfig,
} from "./types";
import {
  getTransactions,
  transactionStatus,
} from "../../../services/blusky/transactions";
import { getAllSettlementsByCustomerAndPeriod } from "../repository/settlement-frequency";

import type { PrismaClient } from "@prisma/client";

/**
 * Main settlement approval function
 */
export const approveSettlement = async (
  prisma: PrismaClient,
  approveParameters: ApproveParameters
) => {
  const {
    serviceNumber,
    fromDate,
    toDate,
    customerSettlementsId,
    customerCustomerTypeId,
  } = approveParameters;

  try {
    await createCustomerSettlementGenerationSchedule(prisma, {
      serviceNumber,
      fromDate,
      toDate,
      customerCustomerTypeId,
      generationType: "FINAL",
      generationStatus: "STARTED",
    });

    const settlement = await getSettlementForApproval(
      prisma,
      customerSettlementsId
    );

    const customerType =
      settlement.customerCustomerType.customerType.customerTypeName;

    await (customerType === "Merchant"
      ? approveMerchantSettlement(prisma, settlement, approveParameters)
      : approveNonMerchantSettlement());

    const scheduleRecord = await createCustomerSettlementGenerationSchedule(
      prisma,
      {
        serviceNumber,
        fromDate,
        toDate,
        customerCustomerTypeId,
        generationType: "FINAL",
        generationStatus: "COMPLETE",
        completionDate: new Date(),
      }
    );

    return {
      success: true,
      message: "Settlement approved successfully",
      scheduleId: scheduleRecord.customerSettlementGenerationScheduleId,
    };
  } catch (error) {
    await createCustomerSettlementGenerationSchedule(prisma, {
      serviceNumber,
      fromDate,
      toDate,
      customerCustomerTypeId,
      generationType: "FINAL",
      generationStatus: "ERROR",
      errorMessage: (error as Error).message,
    });

    throw error;
  }
};

/**
 * Generates Excel files for settlement approval
 * Creates separate files for each platform (IDP, ETF, ETI, RFM) in their respective folders
 */
const generateSettlementExcelFiles = async (
  settlement: ApprovalCustomerSettlement,
  prisma: PrismaClient
): Promise<void> => {
  const settlements = await getAllSettlementsByCustomerAndPeriod(
    settlement.customerId,
    settlement.fromDate!,
    settlement.toDate!,
    prisma
  );

  if (!settlement.customerCustomerType.statementFolderLocation) {
    throw new Error("Statement folder location not configured for customer");
  }

  const baseFolderLocation =
    settlement.customerCustomerType.statementFolderLocation;

  const fullFolderPath = path.join(baseDirectoryName, baseFolderLocation);

  if (!existsSync(fullFolderPath)) {
    mkdirSync(fullFolderPath, {
      recursive: true,
    });
  }

  const { customerTradingName } = settlement.customer;
  const formattedFromDate = format(settlement.fromDate!, "yyyy-MM-dd");
  const formattedToDate = format(settlement.toDate!, "yyyy-MM-dd");

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const transactions = await getTransactions(
    settlement.customer.serviceNumber,
    {
      fromDate: toDateOnly(settlement.fromDate!),
      toDate: toDateOnly(settlement.toDate!),
    },
    {
      aggregate: {
        [transactionStatus.rejected1]: {
          aggregateForPlatforms: ["ETI", "RFM"],
          targetPlatform: "RTO",
          keepInOriginal: false,
        },
      },
    }
  );

  // Process each settlement platform
  for (const settlementItem of settlements) {
    const platformCode = Object.keys(settlementItem)[0];

    if (!platformCode) {
      continue;
    }

    const platformData =
      settlementItem[platformCode as keyof typeof settlementItem];

    // Only generate Excel for pay-in platforms
    if (!payInPlatforms.includes(platformCode as PayInPlatform)) {
      continue;
    }

    if (!platformData?.labelName) {
      continue; // Skip platforms with no data
    }

    const excelConfig: ExcelConfig = {
      platformCode,
      customerTradingName,
      period: {
        fromDate: settlement.fromDate!,
        toDate: settlement.toDate!,
      },
      fileName: `${customerTradingName}-${formattedFromDate}-${formattedToDate}-${platformCode}.xlsx`,
      folderPath: path.join(
        baseDirectoryName,
        baseFolderLocation,
        platformCode
      ),
      sheetName: platformData.labelName ?? `${platformCode} Settlement`,
      data: platformData as SettlementExcelData,
    };

    // eslint-disable-next-line no-await-in-loop
    await generatePlatformExcelFile(excelConfig);
  }
};

/**
 * Creates Excel file for a specific platform
 */
const generatePlatformExcelFile = async (
  config: ExcelConfig
): Promise<void> => {
  const {
    customerTradingName,
    period: { fromDate, toDate },
    fileName,
    folderPath,
    data: {
      labelName,
      transactionCount,
      totalTransactionAmount,
      totalRefundAmount,
      gatewayFee,
      transactionFee,
      salesFee,
      refundFee,
    },
  } = config;

  // Ensure the platform folder exists
  await ensureFolderExists(folderPath);

  // // Create Excel workbook
  const workbook = new ExcelJS.Workbook();

  const summaryWorksheet = workbook.addWorksheet("SUMMARY", {
    views: [{ showGridLines: false }],
  });

  summaryWorksheet.columns = summaryColumns;

  const gigadatLogo = baseDirectoryName + "/logo-gigadat.png";
  const imageBuffer = readFileSync(gigadatLogo);

  const imageId = workbook.addImage({
    buffer: imageBuffer,
    extension: "png",
  });

  summaryWorksheet.addImage(imageId, "B1:C4");
  summaryWorksheet.getColumn("D").numFmt = excelNumberFormat;

  summaryWorksheet.mergeCells("B6:D6");
  summaryWorksheet.getCell("B6").alignment = { horizontal: "center" };
  summaryWorksheet.getCell("B6").font = { bold: true, size: 11 };
  summaryWorksheet.getRow(6).getCell(2).value = customerTradingName;

  summaryWorksheet.mergeCells("B7:D7");
  summaryWorksheet.getCell("B7").alignment = { horizontal: "center" };
  summaryWorksheet.getCell("B7").font = { bold: true, size: 11 };
  summaryWorksheet.getRow(7).getCell(2).value = labelName;

  summaryWorksheet.mergeCells("B9:D9");
  summaryWorksheet.getCell("B9").alignment = { horizontal: "center" };
  summaryWorksheet.getCell("B9").font = {
    bold: true,
  };
  summaryWorksheet.getRow(9).getCell(2).value =
    `${format(fromDate, "MMMM d")} - ${format(toDate, "MMMM d")}`;

  let rowNumber = 12;

  summaryWorksheet.getCell(`B${rowNumber}`).border = {
    bottom: { style: "thick" },
  };
  summaryWorksheet.getCell(`C${rowNumber}`).alignment = {
    horizontal: "center",
  };
  summaryWorksheet.getCell(`C${rowNumber}`).font = { bold: true };
  summaryWorksheet.getCell(`C${rowNumber}`).border = {
    bottom: { style: "thick" },
  };
  summaryWorksheet.getCell(`D${rowNumber}`).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getCell(`D${rowNumber}`).font = { bold: true };
  summaryWorksheet.getCell(`D${rowNumber}`).border = {
    bottom: { style: "thick" },
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).value = "# of Transactions";
  summaryWorksheet.getRow(rowNumber).getCell(4).value = "CAD";

  rowNumber++;

  summaryWorksheet.getRow(rowNumber).getCell(2).value = labelName;
  summaryWorksheet.getRow(rowNumber).getCell(3).value = transactionCount;
  summaryWorksheet.getRow(rowNumber).getCell(4).value = totalTransactionAmount;
  summaryWorksheet.getRow(rowNumber).getCell(4).border = {
    top: { style: "thin" },
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).border = {
    top: { style: "thin" },
  };

  rowNumber += 2;
  summaryWorksheet.getRow(rowNumber).getCell(3).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).font = {
    bold: false,
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).value = "Gateway Fee";
  summaryWorksheet.getRow(rowNumber).getCell(4).value = gatewayFee;

  rowNumber++;
  summaryWorksheet.getRow(rowNumber).getCell(3).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).font = {
    bold: false,
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).value = "Transaction Fee";
  summaryWorksheet.getRow(rowNumber).getCell(4).value = transactionFee;

  rowNumber++;
  summaryWorksheet.getRow(rowNumber).getCell(3).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).font = {
    bold: false,
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).value = "Sales Fee";
  summaryWorksheet.getRow(rowNumber).getCell(4).value = salesFee;

  rowNumber++;

  const transactionsWorksheet = workbook.addWorksheet("Transactions");
  transactionsWorksheet.columns = transactionColumns;

  const refundsWorksheet = workbook.addWorksheet("Refunds");
  refundsWorksheet.columns = transactionColumns;

  summaryWorksheet.getRow(rowNumber).getCell(3).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).font = {
    bold: false,
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).value = "Return Fee";
  summaryWorksheet.getRow(rowNumber).getCell(4).value = refundFee;
  rowNumber++;
  summaryWorksheet.getRow(rowNumber).getCell(3).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).font = {
    bold: false,
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).value = "Returns";
  summaryWorksheet.getRow(rowNumber).getCell(4).value = totalRefundAmount;
  rowNumber++;
  summaryWorksheet.getRow(rowNumber).getCell(3).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).font = { bold: true };
  summaryWorksheet.getRow(rowNumber).getCell(3).value = "TOTAL COSTS";
  const rfmTotalCostsFormula = `SUM(D15:D${rowNumber - 1})`;
  summaryWorksheet.getRow(rowNumber).getCell(4).value = {
    formula: rfmTotalCostsFormula,
  };
  summaryWorksheet.getRow(rowNumber).getCell(4).border = {
    top: { style: "thin" },
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).border = {
    top: { style: "thin" },
  };

  rowNumber += 2;

  summaryWorksheet.getRow(rowNumber).getCell(3).alignment = {
    horizontal: "right",
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).font = { bold: true };
  summaryWorksheet.getRow(rowNumber).getCell(3).value = "Total Payable in CAD";
  const totalPayableFormula = `D13-D${rowNumber - 2}`;
  summaryWorksheet.getRow(rowNumber).getCell(4).value = {
    formula: totalPayableFormula,
  };
  summaryWorksheet.getRow(rowNumber).getCell(4).border = {
    top: { style: "thin" },
  };
  summaryWorksheet.getRow(rowNumber).getCell(3).border = {
    top: { style: "thin" },
  };

  // // Save the Excel file
  const fullFilePath = path.join(folderPath, fileName);

  await workbook.xlsx.writeFile(fullFilePath);
};

/**
 * Ensures the folder exists, creates it if it doesn't
 */
const ensureFolderExists = async (folderPath: string): Promise<void> => {
  if (!existsSync(folderPath)) {
    mkdirSync(folderPath, {
      recursive: true,
    });
  }
};

export const approveMerchantSettlement = async (
  prisma: PrismaClient,
  settlement: ApprovalCustomerSettlement,
  approveParameters: ApproveParameters
) => {
  const { fromDate, toDate, netPayout, userId } = approveParameters;

  // Convert string dates to DateOnly format - renamed variables to avoid conflict
  const convertedFromDate = toDateOnlyFromString(fromDate);
  const convertedToDate = toDateOnlyFromString(toDate);

  const previousDate = getPreviousDay(fromDate);

  console.log(
    { customer: settlement.customer.customerTradingName },
    "====> Generating EXCEL files <====="
  );

  await generateSettlementExcelFiles(settlement, prisma);

  console.log(
    { customer: settlement.customer.customerTradingName },
    "====> Generated EXCEL files !!! <====="
  );

  const [previousSettlement, wires] = await Promise.all([
    fetchPreviousSettlementForEndBalance(
      prisma,
      settlement.customerId,
      settlement.platform.platformId,
      previousDate
    ),
    getWirePayments(
      settlement.customerId,
      convertedFromDate,
      convertedToDate,
      prisma
    ),
  ]);

  const newEndBalance = calculateEndBalance(
    wires,
    previousSettlement,
    netPayout
  );

  if (settlement.customerCustomerType.statementFolderLocation) {
    const [wire] = await Promise.all([
      createWire(prisma, {
        settlement,
        wireAmount: netPayout,
        toDate,
      }),
      updateCustomerSettlementsToApproved(prisma, {
        customerId: settlement.customerId,
        fromDate,
        toDate,
        approvedBy: userId,
      }),
      updateCustomerSettlementWithEndBalance(
        prisma,
        settlement.customerSettlementsId,
        newEndBalance,
        userId
      ),
    ]);

    const emailConfiguration = settlement.customer?.emailConfiguration;

    if (!emailConfiguration || emailConfiguration.isEmailEnabled) {
      await createEmail(prisma, settlement, wire);
    }
  }
};

const getPreviousDay = (fromDate: string): Date => {
  return subDays(new Date(fromDate), 1);
};

const calculateEndBalance = (
  wires: WirePayment[],
  previousSettlement: PreviousSettlement | undefined,
  netPayout: number
): number => {
  let totalWireAmount = 0;

  for (const wire of wires) {
    if (wire.transactionType === "I") {
      totalWireAmount += Number(wire.transactionAmount);
    } else {
      totalWireAmount -= Number(wire.transactionAmount);
    }
  }

  return (
    Number(previousSettlement?.endBalance ?? 0) +
    Number(totalWireAmount ?? 0) +
    Number(netPayout)
  );
};

export const approveNonMerchantSettlement = async () => {
  throw new Error(`NonMerchant settlement approval is not yet implemented.`);
};
