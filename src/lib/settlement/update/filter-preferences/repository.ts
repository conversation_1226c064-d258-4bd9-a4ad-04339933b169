import { type SettlementFilters } from "../../read/types";

import type { PrismaClient } from "@prisma/client";

export const updateSettlementPreference = async (
  userId: number,
  settlementFilters: SettlementFilters,
  prisma: PrismaClient
): Promise<SettlementFilters> => {
  const { settlementPreference } = await prisma.userPreference.update({
    where: { userId },
    data: {
      settlementPreference: settlementFilters,
    },
    select: {
      settlementPreference: true,
    },
  });

  return settlementPreference as SettlementFilters;
};
