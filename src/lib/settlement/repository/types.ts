import { type DateOnly } from "../../../utils/date-only";
import { type SettlementResult } from "../functions/workers/types";

type TierItem = {
  id: number;
  maxAmount: number;
  minAmount: number;
  salesFee: number;
  transactionFee: number;
};

type TierSet = {
  id: number;
  name: string;
  tierItem: TierItem[];
};

const nonMerchantTypes = ["Agent", "Sub Agent", "Integrator"] as const;
type NonMerchantType = (typeof nonMerchantTypes)[number];

type CustomerTypeMember = {
  id: number;
  isCombineIdpTierSet: boolean;
  isCombineAchTierSet: boolean;
  isCombineRtoTierSet: boolean;
  statementFolderLocation: string | undefined;
  statementFrequencyId: number | undefined;
  statementFrequencyName: string | undefined;
  statementFrequencyCode: string | undefined;
  customerTypeId?: number;
  customerTypeName: string;
  serviceNumbers: Array<{
    serviceNumber: string;
    fromDate: DateOnly | undefined;
    toDate: DateOnly | undefined;
  }>;
  volumeCombination: VolumeCombination;
};

type VolumeCombination = Array<{
  fromDate: Date;
  paymentType: PaymentType;
  volumeCombinationPlatform: VolumeCombinationPlatform;
}>;

type PaymentType = {
  paymentTypeName: string;
};

type VolumeCombinationPlatform = Array<{
  enabled: boolean;
  platform: Platform;
}>;

type Platform = {
  platformCode: string;
  platformName: string;
};

type MerchantPlatformMember = {
  id: number;
  delayInMonths: number;
  transactionFee: number;
  gatewayFee: number;
  refundFee: number;
  hasMinimum: boolean;
  minimumThreshold: number;
  minimumCharge: number;
  fromDate: DateOnly | undefined;
  toDate: DateOnly | undefined;
  platformId: number;
  platformCode: string;
  platformName: string;
  platformType: string;
  tierSet: TierSet;
  rejected1TierSet?: TierSet;
  delayTierItem: TierItem | undefined;
  integrator?: NonMerchantPlatformConfiguration;
  agent?: NonMerchantPlatformConfiguration;
  subAgent?: NonMerchantPlatformConfiguration;
};

type MerchantSettlementConfiguration = {
  customerCustomerTypeId: number;
  customerId: number;
  customerName: string;
  customerTradingName: string;
  serviceNumber: string;
  settlementType?: string | undefined;
  isCombineMultipleServices: boolean;
  isCombineMultipleServicesKyc: boolean;
  customerType: CustomerTypeMember;
  platformConfigurations: Record<string, MerchantPlatformMember[]>;
  firstTransactionDate: DateOnly | undefined;
};

type NonMerchantPlatformConfiguration = {
  nonMerchantId: number;
  tierSet: TierSet;
  saleTierSet: TierSet;
  gatewayFeePercentage: number;
  isCumulative: boolean;
};

type NonMerchantSettlementConfiguration = {
  customerCustomerTypeId: number;
  customerId: number;
  customerName: string;
  serviceNumber: string;
  settlementType: string;
  isCombineMultipleServices: boolean;
  combinedMerchantServiceNumbers: string[];
  nonMerchantType: CustomerTypeMember;
  associatedMerchantIds: number[];
};

type AssociatedMerchants = {
  merchantIds: number[];
  serviceNumbers: string[];
};

type SettlementsResult = Record<
  number,
  {
    status: "SUCCESS" | "ERROR" | "SKIPPED";
    message?: string;
    data?: { result: SettlementResult; timeTakenInSeconds: number };
  }
>;

type FrequencySettlementsResult = Record<
  number,
  {
    status: "SUCCESS" | "ERROR" | "SKIPPED";
    generationType: "INITIAL" | "FINAL";
    generationStatus:
      | "STARTED"
      | "PENDING"
      | "COMPLETE"
      | "COMPLETED"
      | "SKIPPED"
      | "FIXED"
      | "ERROR";
    scheduleId: number;
    message?: string;
    data?: {
      result: SettlementResult;
      timeTakenInSeconds: number;
      endBalance?: number;
    };
  }
>;

type FrequencySettlementSchedule = {
  scheduleId: number;
  generationType: "INITIAL" | "FINAL";
  generationStatus:
    | "STARTED"
    | "PENDING"
    | "COMPLETE"
    | "COMPLETED"
    | "SKIPPED"
    | "FIXED"
    | "ERROR";
  errorMessage?: string;
};

type SettlementState =
  | "Processing"
  | "Skipped"
  | "Error"
  | "Approval Pending"
  | "Approval Processing"
  | "Approval Error"
  | "Approval Success"
  | "Status Unknown";

type SettlementSummary = {
  id: string;
  customerName: string;
  serviceNumber: string;
  customerType: string;
  status: SettlementState;
  fromDate: string;
  toDate: string;
  platformSettlements: PlatformSettlements;
  error?: string;
  settlementFolderLocation: string | undefined;
};

type PlatformSettlements = Partial<Record<PlatformCode, SettlementDetails>>;

const platforms = [
  "IDP",
  "ETI",
  "ETF",
  "RFM",
  "ETO",
  "RTO",
  "RTX",
  "ACH",
  "ANR",
  "ANX",
  "KYC",
  "SUMMARY",
] as const;
type PlatformCode = (typeof platforms)[number];

type SettlementDetails = {
  labelName?: string;
  isNonZero?: boolean;
  transactionCount?: number;
  totalTransactionAmount?: number;
  refundCount?: number;
  totalRefundAmount?: number;
  gatewayFee?: number;
  transactionFee?: number;
  salesFee?: number;
  refundFee?: number;
  totalFailedAmount?: number;
  endBalance?: number;
  total2FaRejectAmount?: number;
  total2FaRejectCount?: number;
  txnAmountRTO_R?: number;
  txnCountETI_R1?: number;
  minimumFeeTotal?: number;
  minimumFeeCount?: number;
  totalMinimumAmount?: number;
  partialReturnAmountRTO?: number;
  partialReturnCountRTO?: number;
  totalPayable?: number;
  isAdjusted?: boolean;
  totalPayout?: number;
  totalAdjustments?: number;
  netPayout?: number;
  totalCosts?: number;
  adjustments?: AdjustmentDetails[];
  kycDetails?: KycDetails;
};

type AdjustmentDetails = {
  label: string;
  amount: number;
  displayCommentExcel: boolean;
  comment: string;
};

type KycDetails = {
  KY1?: { transactionCount: number; totalTransactionAmount: number };
  KY2?: { transactionCount: number; totalTransactionAmount: number };
  KY3?: { transactionCount: number; totalTransactionAmount: number };
  KY4?: { transactionCount: number; totalTransactionAmount: number };
  KY5?: { transactionCount: number; totalTransactionAmount: number };
  KY6?: { transactionCount: number; totalTransactionAmount: number };
};

export {
  type TierItem,
  type TierSet,
  type MerchantPlatformMember,
  type CustomerTypeMember,
  type NonMerchantPlatformConfiguration,
  type MerchantSettlementConfiguration,
  type NonMerchantSettlementConfiguration,
  type AssociatedMerchants,
  type SettlementsResult,
  type FrequencySettlementsResult,
  type FrequencySettlementSchedule,
  type SettlementState,
  type SettlementSummary,
  type PlatformCode,
  type NonMerchantType,
  nonMerchantTypes,
  type PlatformSettlements,
  type SettlementDetails,
  type AdjustmentDetails,
  type KycDetails,
};

export enum SettlementStatus {
  SUCCESS = "SUCCESS",
  PROGRESS = "PROGRESS",
  ERROR = "ERROR",
}
