import { Prisma, type PrismaClient } from "@prisma/client";

import { updateMonthlyCustomerSettlement } from "./settlement-monthly";

describe("updateMonthlyCustomerSettlement", () => {
  afterEach(() => {
    vi.clearAllMocks();

    monthlySettlementJob = {
      status: "SUCCESS",
      meta: {
        totalCount: 100,
        successCount: 50,
        errorCount: 25,
        skippedCount: 25,
        timeTakenInSeconds: 1000,
      },
    };
  });

  let monthlySettlementJob = {
    status: "SUCCESS",
    meta: {
      totalCount: 100,
      successCount: 50,
      errorCount: 25,
      skippedCount: 25,
      timeTakenInSeconds: 1000,
    },
  };

  const settlementResultMerchantData = {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    ETI: {
      totalChargedFees: {
        gatewayFeeTotal: 0,
        salesFeeTotal: 233.541,
        transactionFeeTotal: 102.7,
        refundFeeTotal: 0,
        rejected1FeeTotal: 0,
        minimumFeeTotal: 0,
        partialReturnFeeTotal: 0,
      },
      totalTransactionSummary: {
        transactionCount: 158,
        totalTransactionAmount: 4718,
        failedCount: 0,
        totalFailedAmount: 0,
        refundCount: 0,
        totalRefundAmount: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        _RCount: 0,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        total_RAmount: 0,
        rejected1Count: 0,
        totalRejected1Amount: 0,
        minimumAmountCount: 0,
        totalMinimumAmount: 0,
        partialReturnCount: 0,
        totalPartialReturnAmount: 0,
      },
      platformId: 2,
      isContractChange: false,
      feesBreakdown: [
        {
          interval: {
            fromDate: { year: 2024, month: 12, day: 1 },
            toDate: { year: 2024, month: 12, day: 31 },
          },
          chargedFees: {
            gatewayFeeTotal: 0,
            salesFeeTotal: 233.541,
            transactionFeeTotal: 102.7,
            refundFeeTotal: 0,
            rejected1FeeTotal: 0,
            minimumFeeTotal: 0,
            partialReturnFeeTotal: 0,
          },
          meta: {
            rates: {
              transactionFee: 0.65,
              reject1Fee: 0.65,
              salesFee: 0.0495,
              gatewayFee: 0,
              refundFee: 6.95,
              minimumThreshold: 0,
              minimumCharge: 0,
              isMinimumFeeApplicable: false,
              transactionTierItem: {
                id: 232,
                maxAmount: 500_000,
                salesFee: 0.0495,
                minAmount: 0,
                transactionFee: 0.65,
              },
              rejectOneTierItem: {
                id: 232,
                maxAmount: 500_000,
                salesFee: 0.0495,
                minAmount: 0,
                transactionFee: 0.65,
              },
              delayMonths: {
                isDelayApplicable: false,
                delayInMonths: 0,
                tierItem: undefined,
              },
            },
            rateDeterminingProfile: {
              isCombineIdpTierSet: false,
              isCombineRtoTierSet: false,
              isCombineAchTierSet: true,
              fromDate: { year: 2024, month: 11, day: 1 },
              toDate: { year: 2024, month: 12, day: 1 },
              serviceNumbers: [],
              isCombineMultipleServicesEnabled: false,
              combineTotal: 5289,
            },
            transactionSummary: {
              transactionCount: 158,
              totalTransactionAmount: 4718,
              failedCount: 0,
              totalFailedAmount: 0,
              refundCount: 0,
              totalRefundAmount: 0,
              // eslint-disable-next-line @typescript-eslint/naming-convention
              _RCount: 0,
              // eslint-disable-next-line @typescript-eslint/naming-convention
              total_RAmount: 0,
              rejected1Count: 0,
              totalRejected1Amount: 0,
              minimumAmountCount: 0,
              totalMinimumAmount: 0,
              partialReturnCount: 0,
              totalPartialReturnAmount: 0,
            },
          },
        },
      ],
      trueUp: {
        totalChargedFees: {
          gatewayFeeTotal: 0,
          salesFeeTotal: 233.541,
          transactionFeeTotal: 102.7,
          refundFeeTotal: 0,
          rejected1FeeTotal: 0,
          minimumFeeTotal: 0,
          partialReturnFeeTotal: 0,
        },
        totalTransactionSummary: {
          transactionCount: 158,
          totalTransactionAmount: 4718,
          failedCount: 0,
          totalFailedAmount: 0,
          refundCount: 0,
          totalRefundAmount: 0,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          _RCount: 0,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          total_RAmount: 0,
          rejected1Count: 0,
          totalRejected1Amount: 0,
          minimumAmountCount: 0,
          totalMinimumAmount: 0,
          partialReturnCount: 0,
          totalPartialReturnAmount: 0,
        },
        platformId: 2,
        isContractChange: false,
        feesBreakdown: [
          {
            interval: {
              fromDate: { year: 2024, month: 12, day: 1 },
              toDate: { year: 2024, month: 12, day: 31 },
            },
            chargedFees: {
              gatewayFeeTotal: 0,
              salesFeeTotal: 233.541,
              transactionFeeTotal: 102.7,
              refundFeeTotal: 0,
              rejected1FeeTotal: 0,
              minimumFeeTotal: 0,
              partialReturnFeeTotal: 0,
            },
            meta: {
              rates: {
                transactionFee: 0.65,
                reject1Fee: 0.65,
                salesFee: 0.0495,
                gatewayFee: 0,
                refundFee: 6.95,
                minimumThreshold: 0,
                minimumCharge: 0,
                isMinimumFeeApplicable: false,
                transactionTierItem: {
                  id: 232,
                  maxAmount: 500_000,
                  salesFee: 0.0495,
                  minAmount: 0,
                  transactionFee: 0.65,
                },
                rejectOneTierItem: {
                  id: 232,
                  maxAmount: 500_000,
                  salesFee: 0.0495,
                  minAmount: 0,
                  transactionFee: 0.65,
                },
                delayMonths: {
                  isDelayApplicable: false,
                  delayInMonths: 0,
                  tierItem: undefined,
                },
              },
              rateDeterminingProfile: {
                isCombineIdpTierSet: false,
                isCombineRtoTierSet: false,
                isCombineAchTierSet: true,
                fromDate: { year: 2024, month: 12, day: 1 },
                toDate: { year: 2024, month: 12, day: 31 },
                serviceNumbers: [],
                isCombineMultipleServicesEnabled: false,
                combineTotal: 8138,
              },
              transactionSummary: {
                transactionCount: 158,
                totalTransactionAmount: 4718,
                failedCount: 0,
                totalFailedAmount: 0,
                refundCount: 0,
                totalRefundAmount: 0,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                _RCount: 0,
                // eslint-disable-next-line @typescript-eslint/naming-convention
                total_RAmount: 0,
                rejected1Count: 0,
                totalRejected1Amount: 0,
                minimumAmountCount: 0,
                totalMinimumAmount: 0,
                partialReturnCount: 0,
                totalPartialReturnAmount: 0,
              },
            },
          },
        ],
      },
    },
  };

  const mockRecord = {
    customerCustomerTypeId: 1,
    month: 1,
    year: 2021,
    jobId: 2,
  };

  it("should update the monthly customer settlement if previous status == current status", async () => {
    const mockPrismaFunctions = {
      monthlyCustomerSettlement: {
        findUnique: vi.fn().mockResolvedValue(mockRecord),
        update: vi.fn().mockResolvedValue(mockRecord),
      },
      customerSettlements: {
        findMany: vi.fn().mockResolvedValue([]),
      },
      monthlyPlatformSettlement: {
        deleteMany: vi.fn(),
        create: vi.fn(),
      },
      monthlyAllSettlementJob: {
        findUnique: vi.fn(),
      },
    };

    /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    const mockPrisma = {
      monthlyAllSettlementJob: {
        update: vi.fn(),
      },
      $transaction: vi
        .fn()
        .mockImplementation((callback) => callback(mockPrismaFunctions)),
    };
    /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */

    await updateMonthlyCustomerSettlement(
      1,
      { status: "SUCCESS" },
      "SUCCESS",

      // @ts-expect-error - mocking prisma
      mockPrisma as PrismaClient
    );

    expect(
      mockPrismaFunctions.monthlyCustomerSettlement.findUnique
    ).toBeCalledWith({
      where: {
        monthlyCustomerSettlementId: 1,
      },
      select: {
        customerCustomerTypeId: true,
        month: true,
        year: true,
        jobId: true,
      },
    });

    expect(mockPrismaFunctions.monthlyCustomerSettlement.update).toBeCalledWith(
      {
        where: {
          monthlyCustomerSettlementId: 1,
        },
        data: {
          customerCustomerTypeId: 1,
          status: "SUCCESS",
          adjustmentTotal: 0,
          meta: {
            message: undefined,
            timeTakenInSeconds: undefined,
          },
        },
      }
    );

    expect(
      mockPrismaFunctions.monthlyPlatformSettlement.deleteMany
    ).toBeCalledWith({
      where: {
        monthlyCustomerSettlementId: 1,
      },
    });
    expect(
      mockPrismaFunctions.monthlyAllSettlementJob.findUnique
    ).not.toBeCalled();

    expect(mockPrisma.monthlyAllSettlementJob.update).not.toBeCalled();
  });

  it("should calculate adjustment totals", async () => {
    const mockPrismaFunctions = {
      monthlyCustomerSettlement: {
        findUnique: vi.fn().mockResolvedValue(mockRecord),
        update: vi.fn().mockResolvedValue(mockRecord),
      },
      customerSettlements: {
        findMany: vi.fn().mockResolvedValue([
          {
            customerId: 69,
            platformId: 7,
            platform: { platformCode: "KYC", platformId: 7 },
            customerSettlementAdjustments: [],
          },
          {
            customerId: 69,
            platformId: 4,
            platform: { platformCode: "ETI", platformId: 4 },
            customerSettlementAdjustments: [
              { amount: new Prisma.Decimal(-10) },
              { amount: new Prisma.Decimal(-20) },
            ],
          },
          {
            customerId: 69,
            platformId: 13,
            platform: { platformCode: "RTO", platformId: 13 },
            customerSettlementAdjustments: [],
          },
          {
            customerId: 69,
            platformId: 14,
            platform: { platformCode: "RTX", platformId: 14 },
            customerSettlementAdjustments: [],
          },
          {
            customerId: 69,
            platformId: 5,
            platform: { platformCode: "ACH", platformId: 5 },
            customerSettlementAdjustments: [],
          },
          {
            customerId: 69,
            platformId: 16,
            platform: { platformCode: "ANR", platformId: 16 },
            customerSettlementAdjustments: [],
          },
          {
            customerId: 69,
            platformId: 17,
            platform: { platformCode: "ANX", platformId: 17 },
            customerSettlementAdjustments: [],
          },
          {
            customerId: 69,
            platformId: 2,
            platform: { platformCode: "ETO", platformId: 2 },
            customerSettlementAdjustments: [],
          },
          {
            customerId: 69,
            platformId: 3,
            platform: { platformCode: "RFM", platformId: 3 },
            customerSettlementAdjustments: [],
          },
          {
            customerId: 69,
            platformId: 6,
            platform: { platformCode: "IDP", platformId: 6 },
            customerSettlementAdjustments: [],
          },
          {
            customerId: 69,
            platformId: 1,
            platform: { platformCode: "SUMMARY", platformId: 1 },
            customerSettlementAdjustments: [
              { amount: new Prisma.Decimal(-1.5) },
              { amount: new Prisma.Decimal(-29.55) },
            ],
          },
        ]),
      },
      monthlyPlatformSettlement: {
        deleteMany: vi.fn(),
        create: vi.fn(),
      },
      monthlyAllSettlementJob: {
        findUnique: vi.fn(),
      },
    };
    /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    const mockPrisma = {
      monthlyAllSettlementJob: {
        update: vi.fn(),
      },
      $transaction: vi
        .fn()
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .mockImplementation((callback: any) => callback(mockPrismaFunctions)),
    };
    /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    await updateMonthlyCustomerSettlement(
      1,
      // Ts-lint:disable-next-line
      {
        status: "SUCCESS",
        data: {
          result: {
            settlement: settlementResultMerchantData,
          },
          timeTakenInSeconds: 1500,
        },
      },
      "SUCCESS",
      // @ts-expect-error - mocking prisma
      mockPrisma as PrismaClient
    );

    expect(
      mockPrismaFunctions.monthlyCustomerSettlement.findUnique
    ).toBeCalledWith({
      where: {
        monthlyCustomerSettlementId: 1,
      },
      select: {
        customerCustomerTypeId: true,
        month: true,
        year: true,
        jobId: true,
      },
    });

    expect(mockPrismaFunctions.monthlyCustomerSettlement.update).toBeCalledWith(
      {
        where: {
          monthlyCustomerSettlementId: 1,
        },
        data: {
          customerCustomerTypeId: 1,
          status: "SUCCESS",
          adjustmentTotal: -31.05,
          meta: {
            message: undefined,
            timeTakenInSeconds: 1500,
          },
        },
      }
    );

    expect(
      mockPrismaFunctions.monthlyPlatformSettlement.deleteMany
    ).toBeCalledWith({
      where: {
        monthlyCustomerSettlementId: 1,
      },
    });
    expect(
      mockPrismaFunctions.monthlyAllSettlementJob.findUnique
    ).not.toBeCalled();
    expect(mockPrisma.monthlyAllSettlementJob.update).not.toBeCalled();

    expect(mockPrismaFunctions.monthlyPlatformSettlement.create).toBeCalled();
  });

  it("should update the monthly customer settlement and the job meta if previous status !== current status", async () => {
    const mockPrismaFunctions = {
      monthlyCustomerSettlement: {
        findUnique: vi.fn().mockResolvedValue(mockRecord),
        update: vi.fn().mockResolvedValue(mockRecord),
      },
      customerSettlements: {
        findMany: vi.fn().mockResolvedValue([]),
      },
      monthlyPlatformSettlement: {
        deleteMany: vi.fn(),
      },
      monthlyAllSettlementJob: {
        findUnique: vi.fn().mockResolvedValue(monthlySettlementJob),
      },
    };
    /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    const mockPrisma = {
      $transaction: vi
        .fn()

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .mockImplementation((callback: any) => callback(mockPrismaFunctions)),
      monthlyAllSettlementJob: {
        update: vi.fn().mockResolvedValue(2),
      },
    };
    /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    await updateMonthlyCustomerSettlement(
      1,
      { status: "SUCCESS" },
      "ERROR",

      // @ts-expect-error - mocking prisma
      mockPrisma as PrismaClient
    );

    expect(
      mockPrismaFunctions.monthlyCustomerSettlement.findUnique
    ).toBeCalledWith({
      where: {
        monthlyCustomerSettlementId: 1,
      },
      select: {
        customerCustomerTypeId: true,
        month: true,
        year: true,
        jobId: true,
      },
    });

    expect(mockPrismaFunctions.monthlyCustomerSettlement.update).toBeCalledWith(
      {
        where: {
          monthlyCustomerSettlementId: 1,
        },
        data: {
          customerCustomerTypeId: 1,
          status: "SUCCESS",
          adjustmentTotal: 0,
          meta: {
            message: undefined,
            timeTakenInSeconds: undefined,
          },
        },
      }
    );

    expect(
      mockPrismaFunctions.monthlyPlatformSettlement.deleteMany
    ).toBeCalledWith({
      where: {
        monthlyCustomerSettlementId: 1,
      },
    });

    expect(mockPrisma.monthlyAllSettlementJob.update).toBeCalledWith({
      where: {
        monthlyAllSettlementJobId: 2,
      },
      data: {
        status: "SUCCESS",
        meta: {
          totalCount: 100,
          successCount: 51,
          errorCount: 24,
          skippedCount: 25,
          timeTakenInSeconds: 1000,
        },
      },
    });
  });

  it("should update the monthly customer settlement if the previous status was a success and the new status is an error", async () => {
    const mockPrismaFunctions = {
      monthlyCustomerSettlement: {
        findUnique: vi.fn().mockResolvedValue(mockRecord),
        update: vi.fn().mockResolvedValue(mockRecord),
      },
      customerSettlements: {
        findMany: vi.fn().mockResolvedValue([]),
      },
      monthlyPlatformSettlement: {
        deleteMany: vi.fn(),
      },
      monthlyAllSettlementJob: {
        findUnique: vi.fn().mockResolvedValue(monthlySettlementJob),
      },
    };
    /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    const mockPrisma = {
      $transaction: vi
        .fn()

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .mockImplementation((callback: any) => callback(mockPrismaFunctions)),
      monthlyAllSettlementJob: {
        update: vi.fn().mockResolvedValue(2),
      },
    };
    /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    await updateMonthlyCustomerSettlement(
      1,
      { status: "ERROR" },
      "SUCCESS",

      // @ts-expect-error - mocking prisma
      mockPrisma as PrismaClient
    );

    expect(mockPrisma.monthlyAllSettlementJob.update).toBeCalledWith({
      where: {
        monthlyAllSettlementJobId: 2,
      },
      data: {
        status: "SUCCESS",
        meta: {
          totalCount: 100,
          successCount: 49,
          errorCount: 26,
          skippedCount: 25,
          timeTakenInSeconds: 1000,
        },
      },
    });
  });

  it("should throw an error if it can't find a settlement record", async () => {
    const mockPrismaFunctions = {
      monthlyCustomerSettlement: {
        findUnique: vi.fn(),
        update: vi.fn().mockResolvedValue(mockRecord),
      },
      customerSettlements: {
        findMany: vi.fn().mockResolvedValue([]),
      },
      monthlyPlatformSettlement: {
        deleteMany: vi.fn(),
      },
      monthlyAllSettlementJob: {
        findUnique: vi.fn().mockResolvedValue(monthlySettlementJob),
      },
    };
    /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    const mockPrisma = {
      $transaction: vi
        .fn()

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .mockImplementation((callback: any) => callback(mockPrismaFunctions)),
      monthlyAllSettlementJob: {
        update: vi.fn().mockResolvedValue(2),
      },
    };
    /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    await expect(async () => {
      await updateMonthlyCustomerSettlement(
        1,
        { status: "SUCCESS" },
        "ERROR",

        // @ts-expect-error - mocking prisma
        mockPrisma as PrismaClient
      );
    }).rejects.toThrowError("Record not found");

    expect(
      mockPrismaFunctions.monthlyCustomerSettlement.findUnique
    ).toBeCalledWith({
      where: {
        monthlyCustomerSettlementId: 1,
      },
      select: {
        customerCustomerTypeId: true,
        month: true,
        year: true,
        jobId: true,
      },
    });

    expect(
      mockPrismaFunctions.monthlyCustomerSettlement.update
    ).not.toBeCalled();

    expect(
      mockPrismaFunctions.monthlyPlatformSettlement.deleteMany
    ).not.toBeCalled();

    expect(mockPrisma.monthlyAllSettlementJob.update).not.toBeCalled();
  });

  it("should throw an error if it can't find a job id with a settlement record", async () => {
    const mockPrismaFunctions = {
      monthlyCustomerSettlement: {
        findUnique: vi.fn().mockResolvedValue({ jobId: undefined }),
        update: vi.fn().mockResolvedValue(mockRecord),
      },
      customerSettlements: {
        findMany: vi.fn().mockResolvedValue([]),
      },
      monthlyPlatformSettlement: {
        deleteMany: vi.fn(),
      },
      monthlyAllSettlementJob: {
        findUnique: vi.fn().mockResolvedValue(monthlySettlementJob),
      },
    };
    /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    const mockPrisma = {
      $transaction: vi
        .fn()

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .mockImplementation((callback: any) => callback(mockPrismaFunctions)),
      monthlyAllSettlementJob: {
        update: vi.fn().mockResolvedValue(2),
      },
    };
    /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    await expect(async () => {
      await updateMonthlyCustomerSettlement(
        1,
        { status: "SUCCESS" },
        "ERROR",

        // @ts-expect-error - mocking prisma
        mockPrisma as PrismaClient
      );
    }).rejects.toThrowError("Record does not have a correlated job");

    expect(
      mockPrismaFunctions.monthlyCustomerSettlement.findUnique
    ).toBeCalledWith({
      where: {
        monthlyCustomerSettlementId: 1,
      },
      select: {
        customerCustomerTypeId: true,
        month: true,
        year: true,
        jobId: true,
      },
    });

    expect(
      mockPrismaFunctions.monthlyCustomerSettlement.update
    ).not.toBeCalled();

    expect(
      mockPrismaFunctions.monthlyPlatformSettlement.deleteMany
    ).not.toBeCalled();

    expect(mockPrisma.monthlyAllSettlementJob.update).not.toBeCalled();
  });

  it("should throw an error if it can't find a job with the job id", async () => {
    const mockPrismaFunctions = {
      monthlyCustomerSettlement: {
        findUnique: vi.fn().mockResolvedValue(mockRecord),
        update: vi.fn().mockResolvedValue(mockRecord),
      },
      customerSettlements: {
        findMany: vi.fn().mockResolvedValue([]),
      },
      monthlyPlatformSettlement: {
        deleteMany: vi.fn(),
      },
      monthlyAllSettlementJob: {
        findUnique: vi.fn(),
      },
    };
    /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    const mockPrisma = {
      $transaction: vi
        .fn()

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .mockImplementation((callback: any) => callback(mockPrismaFunctions)),
      monthlyAllSettlementJob: {
        update: vi.fn(),
      },
    };
    /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    await expect(async () => {
      await updateMonthlyCustomerSettlement(
        1,
        { status: "SUCCESS" },
        "ERROR",

        // @ts-expect-error - mocking prisma
        mockPrisma as PrismaClient
      );
    }).rejects.toThrowError("Job record not found");

    expect(
      mockPrismaFunctions.monthlyCustomerSettlement.findUnique
    ).toBeCalledWith({
      where: {
        monthlyCustomerSettlementId: 1,
      },
      select: {
        customerCustomerTypeId: true,
        month: true,
        year: true,
        jobId: true,
      },
    });

    expect(mockPrismaFunctions.monthlyCustomerSettlement.update).toBeCalledWith(
      {
        where: {
          monthlyCustomerSettlementId: 1,
        },
        data: {
          customerCustomerTypeId: 1,
          status: "SUCCESS",
          adjustmentTotal: 0,
          meta: {
            message: undefined,
            timeTakenInSeconds: undefined,
          },
        },
      }
    );

    expect(
      mockPrismaFunctions.monthlyAllSettlementJob.findUnique
    ).toBeCalledWith({
      where: {
        monthlyAllSettlementJobId: 2,
      },
      select: {
        status: true,
        meta: true,
      },
    });

    expect(
      mockPrismaFunctions.monthlyPlatformSettlement.deleteMany
    ).not.toBeCalled();

    expect(mockPrisma.monthlyAllSettlementJob.update).not.toBeCalled();
  });

  it("should throw an error if the job found is in error state", async () => {
    const mockPrismaFunctions = {
      monthlyCustomerSettlement: {
        findUnique: vi.fn().mockResolvedValue(mockRecord),
        update: vi.fn().mockResolvedValue(mockRecord),
      },
      customerSettlements: {
        findMany: vi.fn().mockResolvedValue([]),
      },
      monthlyPlatformSettlement: {
        deleteMany: vi.fn(),
      },
      monthlyAllSettlementJob: {
        findUnique: vi
          .fn()
          .mockResolvedValue({ ...monthlySettlementJob, status: "ERROR" }),
      },
    };
    /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    const mockPrisma = {
      $transaction: vi
        .fn()

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .mockImplementation((callback: any) => callback(mockPrismaFunctions)),
      monthlyAllSettlementJob: {
        update: vi.fn().mockResolvedValue(2),
      },
    };
    /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    await expect(async () => {
      await updateMonthlyCustomerSettlement(
        1,
        { status: "SUCCESS" },
        "ERROR",

        // @ts-expect-error - mocking prisma
        mockPrisma as PrismaClient
      );
    }).rejects.toThrowError("Job record found is with an error");

    expect(
      mockPrismaFunctions.monthlyCustomerSettlement.findUnique
    ).toBeCalledWith({
      where: {
        monthlyCustomerSettlementId: 1,
      },
      select: {
        customerCustomerTypeId: true,
        month: true,
        year: true,
        jobId: true,
      },
    });

    expect(mockPrismaFunctions.monthlyCustomerSettlement.update).toBeCalledWith(
      {
        where: {
          monthlyCustomerSettlementId: 1,
        },
        data: {
          customerCustomerTypeId: 1,
          status: "SUCCESS",
          adjustmentTotal: 0,
          meta: {
            message: undefined,
            timeTakenInSeconds: undefined,
          },
        },
      }
    );

    expect(
      mockPrismaFunctions.monthlyAllSettlementJob.findUnique
    ).toBeCalledWith({
      where: {
        monthlyAllSettlementJobId: 2,
      },
      select: {
        status: true,
        meta: true,
      },
    });

    expect(
      mockPrismaFunctions.monthlyPlatformSettlement.deleteMany
    ).not.toBeCalled();

    expect(mockPrisma.monthlyAllSettlementJob.update).not.toBeCalled();
  });

  it("should throw an error if the job found is in progress state", async () => {
    const mockPrismaFunctions = {
      monthlyCustomerSettlement: {
        findUnique: vi.fn().mockResolvedValue(mockRecord),
        update: vi.fn().mockResolvedValue(mockRecord),
      },
      customerSettlements: {
        findMany: vi.fn().mockResolvedValue([]),
      },
      monthlyPlatformSettlement: {
        deleteMany: vi.fn(),
      },
      monthlyAllSettlementJob: {
        findUnique: vi
          .fn()
          .mockResolvedValue({ ...monthlySettlementJob, status: "PROGRESS" }),
      },
    };
    /* eslint-disable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    const mockPrisma = {
      $transaction: vi
        .fn()

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .mockImplementation((callback: any) => callback(mockPrismaFunctions)),
      monthlyAllSettlementJob: {
        update: vi.fn().mockResolvedValue(2),
      },
    };
    /* eslint-enable @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return */
    await expect(async () => {
      await updateMonthlyCustomerSettlement(
        1,
        { status: "SUCCESS" },
        "ERROR",

        // @ts-expect-error - mocking prisma
        mockPrisma as PrismaClient
      );
    }).rejects.toThrowError("Job record found is in progress");

    expect(
      mockPrismaFunctions.monthlyCustomerSettlement.findUnique
    ).toBeCalledWith({
      where: {
        monthlyCustomerSettlementId: 1,
      },
      select: {
        customerCustomerTypeId: true,
        month: true,
        year: true,
        jobId: true,
      },
    });

    expect(mockPrismaFunctions.monthlyCustomerSettlement.update).toBeCalledWith(
      {
        where: {
          monthlyCustomerSettlementId: 1,
        },
        data: {
          customerCustomerTypeId: 1,
          status: "SUCCESS",
          adjustmentTotal: 0,
          meta: {
            message: undefined,
            timeTakenInSeconds: undefined,
          },
        },
      }
    );

    expect(
      mockPrismaFunctions.monthlyAllSettlementJob.findUnique
    ).toBeCalledWith({
      where: {
        monthlyAllSettlementJobId: 2,
      },
      select: {
        status: true,
        meta: true,
      },
    });

    expect(
      mockPrismaFunctions.monthlyPlatformSettlement.deleteMany
    ).not.toBeCalled();

    expect(mockPrisma.monthlyAllSettlementJob.update).not.toBeCalled();
  });
});
